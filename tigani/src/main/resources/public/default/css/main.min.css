/*! tailwindcss v4.0.8 | MIT License | https://tailwindcss.com */
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-red-50: oklch(0.971 0.013 17.38);
    --color-red-100: oklch(0.936 0.032 17.717);
    --color-red-200: oklch(0.885 0.062 18.334);
    --color-red-400: oklch(0.704 0.191 22.216);
    --color-red-500: oklch(0.637 0.237 25.331);
    --color-red-600: oklch(0.577 0.245 27.325);
    --color-red-800: oklch(0.444 0.177 26.899);
    --color-yellow-100: oklch(0.973 0.071 103.193);
    --color-yellow-500: oklch(0.795 0.184 86.047);
    --color-yellow-800: oklch(0.476 0.114 61.907);
    --color-green-100: oklch(0.962 0.044 156.743);
    --color-green-500: oklch(0.723 0.219 149.579);
    --color-green-600: oklch(0.627 0.194 149.214);
    --color-green-800: oklch(0.448 0.119 151.328);
    --color-teal-400: oklch(0.777 0.152 181.912);
    --color-teal-500: oklch(0.704 0.14 182.503);
    --color-teal-600: oklch(0.6 0.118 184.704);
    --color-blue-100: oklch(0.932 0.032 255.585);
    --color-blue-300: oklch(0.809 0.105 251.813);
    --color-blue-400: oklch(0.707 0.165 254.624);
    --color-blue-500: oklch(0.623 0.214 259.815);
    --color-blue-600: oklch(0.546 0.245 262.881);
    --color-blue-700: oklch(0.488 0.243 264.376);
    --color-blue-800: oklch(0.424 0.199 265.638);
    --color-indigo-200: oklch(0.87 0.065 274.039);
    --color-indigo-300: oklch(0.785 0.115 274.713);
    --color-indigo-400: oklch(0.673 0.182 276.935);
    --color-indigo-500: oklch(0.585 0.233 277.117);
    --color-indigo-600: oklch(0.511 0.262 276.966);
    --color-indigo-700: oklch(0.457 0.24 277.023);
    --color-violet-200: oklch(0.894 0.057 293.283);
    --color-violet-500: oklch(0.606 0.25 292.717);
    --color-violet-800: oklch(0.432 0.232 292.759);
    --color-purple-100: oklch(0.946 0.033 307.174);
    --color-purple-500: oklch(0.627 0.265 303.9);
    --color-purple-600: oklch(0.558 0.288 302.321);
    --color-purple-800: oklch(0.438 0.218 303.724);
    --color-pink-500: oklch(0.656 0.241 354.308);
    --color-gray-50: oklch(0.985 0.002 247.839);
    --color-gray-100: oklch(0.967 0.003 264.542);
    --color-gray-200: oklch(0.928 0.006 264.531);
    --color-gray-300: oklch(0.872 0.01 258.338);
    --color-gray-400: oklch(0.707 0.022 261.325);
    --color-gray-500: oklch(0.551 0.027 264.364);
    --color-gray-600: oklch(0.446 0.03 256.802);
    --color-gray-700: oklch(0.373 0.034 259.733);
    --color-gray-800: oklch(0.278 0.033 256.848);
    --color-gray-900: oklch(0.21 0.034 264.665);
    --color-neutral-200: oklch(0.922 0 0);
    --color-neutral-300: oklch(0.87 0 0);
    --color-neutral-400: oklch(0.708 0 0);
    --color-neutral-500: oklch(0.556 0 0);
    --color-neutral-600: oklch(0.439 0 0);
    --color-neutral-700: oklch(0.371 0 0);
    --color-neutral-800: oklch(0.269 0 0);
    --color-neutral-900: oklch(0.205 0 0);
    --color-neutral-950: oklch(0.145 0 0);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-xs: 20rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-xl: 36rem;
    --container-4xl: 56rem;
    --container-5xl: 64rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --radius-xs: 0.125rem;
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
    --blur-md: 12px;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-font-feature-settings: var(--font-sans--font-feature-settings);
    --default-font-variation-settings: var(
      --font-sans--font-variation-settings
    );
    --default-mono-font-family: var(--font-mono);
    --default-mono-font-feature-settings: var(
      --font-mono--font-feature-settings
    );
    --default-mono-font-variation-settings: var(
      --font-mono--font-variation-settings
    );
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var( --default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji" );
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var( --default-font-variation-settings, normal );
    -webkit-tap-highlight-color: transparent;
  }
  body {
    line-height: inherit;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var( --default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace );
    font-feature-settings: var( --default-mono-font-feature-settings, normal );
    font-variation-settings: var( --default-mono-font-variation-settings, normal );
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
    color: color-mix(in oklab, currentColor 50%, transparent);
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .\@container {
    container-type: inline-size;
  }
  .\@container-\[inline-size\] {
    container-type: inline-size;
  }
  .pointer-events-auto {
    pointer-events: auto;
  }
  .pointer-events-none {
    pointer-events: none;
  }
  .\!visible {
    visibility: visible !important;
  }
  .collapse {
    visibility: collapse;
  }
  .invisible {
    visibility: hidden;
  }
  .visible {
    visibility: visible;
  }
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
  .not-sr-only {
    position: static;
    width: auto;
    height: auto;
    padding: 0;
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }
  .\!relative {
    position: relative !important;
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .sticky {
    position: sticky;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .inset-x-0 {
    inset-inline: calc(var(--spacing) * 0);
  }
  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }
  .-start-3 {
    inset-inline-start: calc(var(--spacing) * -3);
  }
  .start-0 {
    inset-inline-start: calc(var(--spacing) * 0);
  }
  .start-0\.5 {
    inset-inline-start: calc(var(--spacing) * 0.5);
  }
  .start-2 {
    inset-inline-start: calc(var(--spacing) * 2);
  }
  .start-5 {
    inset-inline-start: calc(var(--spacing) * 5);
  }
  .-end-3 {
    inset-inline-end: calc(var(--spacing) * -3);
  }
  .end-0 {
    inset-inline-end: calc(var(--spacing) * 0);
  }
  .end-1 {
    inset-inline-end: calc(var(--spacing) * 1);
  }
  .end-1\.5 {
    inset-inline-end: calc(var(--spacing) * 1.5);
  }
  .end-2 {
    inset-inline-end: calc(var(--spacing) * 2);
  }
  .end-2\.5 {
    inset-inline-end: calc(var(--spacing) * 2.5);
  }
  .end-3 {
    inset-inline-end: calc(var(--spacing) * 3);
  }
  .end-4 {
    inset-inline-end: calc(var(--spacing) * 4);
  }
  .end-10 {
    inset-inline-end: calc(var(--spacing) * 10);
  }
  .end-full {
    inset-inline-end: 100%;
  }
  .-top-10 {
    top: calc(var(--spacing) * -10);
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .top-1 {
    top: calc(var(--spacing) * 1);
  }
  .top-1\/2 {
    top: calc(1/2 * 100%);
  }
  .top-2 {
    top: calc(var(--spacing) * 2);
  }
  .top-3 {
    top: calc(var(--spacing) * 3);
  }
  .top-4 {
    top: calc(var(--spacing) * 4);
  }
  .top-full {
    top: 100%;
  }
  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }
  .bottom-full {
    bottom: 100%;
  }
  .isolate {
    isolation: isolate;
  }
  .isolation-auto {
    isolation: auto;
  }
  .z-1 {
    z-index: 1;
  }
  .z-2 {
    z-index: 2;
  }
  .z-10 {
    z-index: 10;
  }
  .z-20 {
    z-index: 20;
  }
  .z-50 {
    z-index: 50;
  }
  .z-60 {
    z-index: 60;
  }
  .z-80 {
    z-index: 80;
  }
  .z-90 {
    z-index: 90;
  }
  .z-\[80\] {
    z-index: 80;
  }
  .z-auto {
    z-index: auto;
  }
  .order-1 {
    order: 1;
  }
  .order-2 {
    order: 2;
  }
  .order-first {
    order: -9999;
  }
  .order-last {
    order: 9999;
  }
  .order-none {
    order: 0;
  }
  .col-auto {
    grid-column: auto;
  }
  .col-span-1 {
    grid-column: span 1 / span 1;
  }
  .col-span-3 {
    grid-column: span 3 / span 3;
  }
  .col-span-full {
    grid-column: 1 / -1;
  }
  .col-start-auto {
    grid-column-start: auto;
  }
  .col-end-auto {
    grid-column-end: auto;
  }
  .row-auto {
    grid-row: auto;
  }
  .row-span-full {
    grid-row: 1 / -1;
  }
  .row-start-auto {
    grid-row-start: auto;
  }
  .row-end-auto {
    grid-row-end: auto;
  }
  .float-end {
    float: inline-end;
  }
  .float-left {
    float: left;
  }
  .float-none {
    float: none;
  }
  .float-right {
    float: right;
  }
  .float-start {
    float: inline-start;
  }
  .clear-both {
    clear: both;
  }
  .clear-end {
    clear: inline-end;
  }
  .clear-left {
    clear: left;
  }
  .clear-none {
    clear: none;
  }
  .clear-right {
    clear: right;
  }
  .clear-start {
    clear: inline-start;
  }
  .\!container {
    width: 100% !important;
    @media (width >= 40rem) {
      max-width: 40rem !important;
    }
    @media (width >= 48rem) {
      max-width: 48rem !important;
    }
    @media (width >= 64rem) {
      max-width: 64rem !important;
    }
    @media (width >= 80rem) {
      max-width: 80rem !important;
    }
    @media (width >= 96rem) {
      max-width: 96rem !important;
    }
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .container\! {
    width: 100% !important;
    @media (width >= 40rem) {
      max-width: 40rem !important;
    }
    @media (width >= 48rem) {
      max-width: 48rem !important;
    }
    @media (width >= 64rem) {
      max-width: 64rem !important;
    }
    @media (width >= 80rem) {
      max-width: 80rem !important;
    }
    @media (width >= 96rem) {
      max-width: 96rem !important;
    }
  }
  .m-0 {
    margin: calc(var(--spacing) * 0);
  }
  .m-1 {
    margin: calc(var(--spacing) * 1);
  }
  .m-2 {
    margin: calc(var(--spacing) * 2);
  }
  .m-3 {
    margin: calc(var(--spacing) * 3);
  }
  .m-32 {
    margin: calc(var(--spacing) * 32);
  }
  .m-48 {
    margin: calc(var(--spacing) * 48);
  }
  .m-px {
    margin: 1px;
  }
  .-mx-1 {
    margin-inline: calc(var(--spacing) * -1);
  }
  .-mx-1\.5 {
    margin-inline: calc(var(--spacing) * -1.5);
  }
  .-mx-2 {
    margin-inline: calc(var(--spacing) * -2);
  }
  .-mx-2\.5 {
    margin-inline: calc(var(--spacing) * -2.5);
  }
  .mx-1 {
    margin-inline: calc(var(--spacing) * 1);
  }
  .mx-1\.5 {
    margin-inline: calc(var(--spacing) * 1.5);
  }
  .mx-3 {
    margin-inline: calc(var(--spacing) * 3);
  }
  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }
  .mx-auto {
    margin-inline: auto;
  }
  .-my-1 {
    margin-block: calc(var(--spacing) * -1);
  }
  .-my-1\.5 {
    margin-block: calc(var(--spacing) * -1.5);
  }
  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }
  .my-2 {
    margin-block: calc(var(--spacing) * 2);
  }
  .my-auto {
    margin-block: auto;
  }
  .\!ms-auto {
    margin-inline-start: auto !important;
  }
  .-ms-2 {
    margin-inline-start: calc(var(--spacing) * -2);
  }
  .ms-0 {
    margin-inline-start: calc(var(--spacing) * 0);
  }
  .ms-0\.5 {
    margin-inline-start: calc(var(--spacing) * 0.5);
  }
  .ms-1 {
    margin-inline-start: calc(var(--spacing) * 1);
  }
  .ms-1\.5 {
    margin-inline-start: calc(var(--spacing) * 1.5);
  }
  .ms-2 {
    margin-inline-start: calc(var(--spacing) * 2);
  }
  .ms-3 {
    margin-inline-start: calc(var(--spacing) * 3);
  }
  .ms-4 {
    margin-inline-start: calc(var(--spacing) * 4);
  }
  .ms-auto {
    margin-inline-start: auto;
  }
  .\!me-1 {
    margin-inline-end: calc(var(--spacing) * 1) !important;
  }
  .\!me-1\.5 {
    margin-inline-end: calc(var(--spacing) * 1.5) !important;
  }
  .-me-0 {
    margin-inline-end: calc(var(--spacing) * -0);
  }
  .-me-0\.5 {
    margin-inline-end: calc(var(--spacing) * -0.5);
  }
  .-me-1 {
    margin-inline-end: calc(var(--spacing) * -1);
  }
  .-me-1\.5 {
    margin-inline-end: calc(var(--spacing) * -1.5);
  }
  .me-1 {
    margin-inline-end: calc(var(--spacing) * 1);
  }
  .me-1\.5 {
    margin-inline-end: calc(var(--spacing) * 1.5);
  }
  .me-2 {
    margin-inline-end: calc(var(--spacing) * 2);
  }
  .me-2\.5 {
    margin-inline-end: calc(var(--spacing) * 2.5);
  }
  .me-3 {
    margin-inline-end: calc(var(--spacing) * 3);
  }
  .me-5 {
    margin-inline-end: calc(var(--spacing) * 5);
  }
  .me-auto {
    margin-inline-end: auto;
  }
  .-mt-1 {
    margin-top: calc(var(--spacing) * -1);
  }
  .-mt-1\.5 {
    margin-top: calc(var(--spacing) * -1.5);
  }
  .mt-0 {
    margin-top: calc(var(--spacing) * 0);
  }
  .mt-0\.5 {
    margin-top: calc(var(--spacing) * 0.5);
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }
  .mt-1\.5 {
    margin-top: calc(var(--spacing) * 1.5);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-5 {
    margin-top: calc(var(--spacing) * 5);
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .mb-0 {
    margin-bottom: calc(var(--spacing) * 0);
  }
  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-2\.5 {
    margin-bottom: calc(var(--spacing) * 2.5);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-5 {
    margin-bottom: calc(var(--spacing) * 5);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }
  .box-border {
    box-sizing: border-box;
  }
  .box-content {
    box-sizing: content-box;
  }
  .form-checkbox {
    appearance: none;
    padding: 0;
    print-color-adjust: exact;
    display: inline-block;
    vertical-align: middle;
    background-origin: border-box;
    user-select: none;
    flex-shrink: 0;
    height: 1rem;
    width: 1rem;
    color: oklch(0.546 0.245 262.881);
    background-color: #fff;
    border-color: oklch(0.551 0.027 264.364);
    border-width: 1px;
    --tw-shadow: 0 0 #0000;
    border-radius: 0px;
    &:focus {
      outline: 2px solid transparent;
      outline-offset: 2px;
      --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-color: #fff;
      --tw-ring-color: oklch(0.546 0.245 262.881);
      --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
      --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
      box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
    &:checked {
      border-color: transparent;
      background-color: currentColor;
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
    }
    &:checked {
      background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
      @media (forced-colors: active) {
        appearance: auto;
      }
    }
    &:checked:hover {
      border-color: transparent;
      background-color: currentColor;
    }
    &:checked:focus {
      border-color: transparent;
      background-color: currentColor;
    }
    &:indeterminate {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
      border-color: transparent;
      background-color: currentColor;
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
      @media (forced-colors: active) {
        appearance: auto;
      }
    }
    &:indeterminate:hover {
      border-color: transparent;
      background-color: currentColor;
    }
    &:indeterminate:focus {
      border-color: transparent;
      background-color: currentColor;
    }
  }
  .form-radio {
    appearance: none;
    padding: 0;
    print-color-adjust: exact;
    display: inline-block;
    vertical-align: middle;
    background-origin: border-box;
    user-select: none;
    flex-shrink: 0;
    height: 1rem;
    width: 1rem;
    color: oklch(0.546 0.245 262.881);
    background-color: #fff;
    border-color: oklch(0.551 0.027 264.364);
    border-width: 1px;
    --tw-shadow: 0 0 #0000;
    border-radius: 100%;
    &:focus {
      outline: 2px solid transparent;
      outline-offset: 2px;
      --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-color: #fff;
      --tw-ring-color: oklch(0.546 0.245 262.881);
      --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
      --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
      box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
    &:checked {
      border-color: transparent;
      background-color: currentColor;
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
    }
    &:checked {
      background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
      @media (forced-colors: active) {
        appearance: auto;
      }
    }
    &:checked:hover {
      border-color: transparent;
      background-color: currentColor;
    }
    &:checked:focus {
      border-color: transparent;
      background-color: currentColor;
    }
  }
  .form-input {
    appearance: none;
    background-color: #fff;
    border-color: oklch(0.551 0.027 264.364);
    border-width: 1px;
    border-radius: 0px;
    padding-top: 0.5rem;
    padding-right: 0.75rem;
    padding-bottom: 0.5rem;
    padding-left: 0.75rem;
    font-size: 1rem;
    line-height: 1.5rem;
    --tw-shadow: 0 0 #0000;
    &:focus {
      outline: 2px solid transparent;
      outline-offset: 2px;
      --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-color: oklch(0.546 0.245 262.881);
      --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
      --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
      box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      border-color: oklch(0.546 0.245 262.881);
    }
    &::placeholder {
      color: oklch(0.551 0.027 264.364);
      opacity: 1;
    }
    &::-webkit-datetime-edit-fields-wrapper {
      padding: 0;
    }
    &::-webkit-date-and-time-value {
      min-height: 1.5em;
    }
    &::-webkit-date-and-time-value {
      text-align: inherit;
    }
    &::-webkit-datetime-edit {
      display: inline-flex;
    }
    &::-webkit-datetime-edit {
      padding-top: 0;
      padding-bottom: 0;
    }
    &::-webkit-datetime-edit-year-field {
      padding-top: 0;
      padding-bottom: 0;
    }
    &::-webkit-datetime-edit-month-field {
      padding-top: 0;
      padding-bottom: 0;
    }
    &::-webkit-datetime-edit-day-field {
      padding-top: 0;
      padding-bottom: 0;
    }
    &::-webkit-datetime-edit-hour-field {
      padding-top: 0;
      padding-bottom: 0;
    }
    &::-webkit-datetime-edit-minute-field {
      padding-top: 0;
      padding-bottom: 0;
    }
    &::-webkit-datetime-edit-second-field {
      padding-top: 0;
      padding-bottom: 0;
    }
    &::-webkit-datetime-edit-millisecond-field {
      padding-top: 0;
      padding-bottom: 0;
    }
    &::-webkit-datetime-edit-meridiem-field {
      padding-top: 0;
      padding-bottom: 0;
    }
  }
  .line-clamp-none {
    overflow: visible;
    display: block;
    -webkit-box-orient: horizontal;
    -webkit-line-clamp: unset;
  }
  .\!flex {
    display: flex !important;
  }
  .\!grid {
    display: grid !important;
  }
  .\!hidden {
    display: none !important;
  }
  .\!inline {
    display: inline !important;
  }
  .\!table {
    display: table !important;
  }
  .block {
    display: block;
  }
  .block\! {
    display: block !important;
  }
  .contents {
    display: contents;
  }
  .flex {
    display: flex;
  }
  .flow-root {
    display: flow-root;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline {
    display: inline;
  }
  .inline-block {
    display: inline-block;
  }
  .inline-flex {
    display: inline-flex;
  }
  .inline-grid {
    display: inline-grid;
  }
  .inline-table {
    display: inline-table;
  }
  .list-item {
    display: list-item;
  }
  .table {
    display: table;
  }
  .table\! {
    display: table !important;
  }
  .table-caption {
    display: table-caption;
  }
  .table-cell {
    display: table-cell;
  }
  .table-column {
    display: table-column;
  }
  .table-column-group {
    display: table-column-group;
  }
  .table-footer-group {
    display: table-footer-group;
  }
  .table-header-group {
    display: table-header-group;
  }
  .table-row {
    display: table-row;
  }
  .table-row-group {
    display: table-row-group;
  }
  .field-sizing-content {
    field-sizing: content;
  }
  .field-sizing-fixed {
    field-sizing: fixed;
  }
  .aspect-auto {
    aspect-ratio: auto;
  }
  .aspect-square {
    aspect-ratio: 1 / 1;
  }
  .size-2 {
    width: calc(var(--spacing) * 2);
    height: calc(var(--spacing) * 2);
  }
  .size-2\.5 {
    width: calc(var(--spacing) * 2.5);
    height: calc(var(--spacing) * 2.5);
  }
  .size-3 {
    width: calc(var(--spacing) * 3);
    height: calc(var(--spacing) * 3);
  }
  .size-3\.5 {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
  }
  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }
  .size-4\.5 {
    width: calc(var(--spacing) * 4.5);
    height: calc(var(--spacing) * 4.5);
  }
  .size-5 {
    width: calc(var(--spacing) * 5);
    height: calc(var(--spacing) * 5);
  }
  .size-6 {
    width: calc(var(--spacing) * 6);
    height: calc(var(--spacing) * 6);
  }
  .size-7 {
    width: calc(var(--spacing) * 7);
    height: calc(var(--spacing) * 7);
  }
  .size-8 {
    width: calc(var(--spacing) * 8);
    height: calc(var(--spacing) * 8);
  }
  .size-8\.5 {
    width: calc(var(--spacing) * 8.5);
    height: calc(var(--spacing) * 8.5);
  }
  .size-9 {
    width: calc(var(--spacing) * 9);
    height: calc(var(--spacing) * 9);
  }
  .size-9\.5 {
    width: calc(var(--spacing) * 9.5);
    height: calc(var(--spacing) * 9.5);
  }
  .size-10 {
    width: calc(var(--spacing) * 10);
    height: calc(var(--spacing) * 10);
  }
  .size-12 {
    width: calc(var(--spacing) * 12);
    height: calc(var(--spacing) * 12);
  }
  .size-20 {
    width: calc(var(--spacing) * 20);
    height: calc(var(--spacing) * 20);
  }
  .size-full {
    width: 100%;
    height: 100%;
  }
  .size-px {
    width: 1px;
    height: 1px;
  }
  .\!h-2 {
    height: calc(var(--spacing) * 2) !important;
  }
  .\!h-2\.5 {
    height: calc(var(--spacing) * 2.5) !important;
  }
  .h-1 {
    height: calc(var(--spacing) * 1);
  }
  .h-1\.5 {
    height: calc(var(--spacing) * 1.5);
  }
  .h-2 {
    height: calc(var(--spacing) * 2);
  }
  .h-2\.5 {
    height: calc(var(--spacing) * 2.5);
  }
  .h-3 {
    height: calc(var(--spacing) * 3);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-7 {
    height: calc(var(--spacing) * 7);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-9 {
    height: calc(var(--spacing) * 9);
  }
  .h-9\.5 {
    height: calc(var(--spacing) * 9.5);
  }
  .h-10 {
    height: calc(var(--spacing) * 10);
  }
  .h-11 {
    height: calc(var(--spacing) * 11);
  }
  .h-11\.5 {
    height: calc(var(--spacing) * 11.5);
  }
  .h-12 {
    height: calc(var(--spacing) * 12);
  }
  .h-15 {
    height: calc(var(--spacing) * 15);
  }
  .h-15\.5 {
    height: calc(var(--spacing) * 15.5);
  }
  .h-24 {
    height: calc(var(--spacing) * 24);
  }
  .h-32 {
    height: calc(var(--spacing) * 32);
  }
  .h-72 {
    height: calc(var(--spacing) * 72);
  }
  .h-82 {
    height: calc(var(--spacing) * 82);
  }
  .h-90 {
    height: calc(var(--spacing) * 90);
  }
  .h-100 {
    height: calc(var(--spacing) * 100);
  }
  .h-120 {
    height: calc(var(--spacing) * 120);
  }
  .h-125 {
    height: calc(var(--spacing) * 125);
  }
  .h-137 {
    height: calc(var(--spacing) * 137);
  }
  .h-137\.5 {
    height: calc(var(--spacing) * 137.5);
  }
  .h-\[17px\] {
    height: 17px;
  }
  .h-\[calc\(100\%-56px\)\] {
    height: calc(100% - 56px);
  }
  .h-auto {
    height: auto;
  }
  .h-full {
    height: 100%;
  }
  .h-screen {
    height: 100vh;
  }
  .max-h-60 {
    max-height: calc(var(--spacing) * 60);
  }
  .max-h-72 {
    max-height: calc(var(--spacing) * 72);
  }
  .max-h-full {
    max-height: 100%;
  }
  .max-h-none {
    max-height: none;
  }
  .max-h-screen {
    max-height: 100vh;
  }
  .min-h-4 {
    min-height: calc(var(--spacing) * 4);
  }
  .min-h-4\.5 {
    min-height: calc(var(--spacing) * 4.5);
  }
  .min-h-7 {
    min-height: calc(var(--spacing) * 7);
  }
  .min-h-7\.5 {
    min-height: calc(var(--spacing) * 7.5);
  }
  .min-h-9 {
    min-height: calc(var(--spacing) * 9);
  }
  .min-h-9\.5 {
    min-height: calc(var(--spacing) * 9.5);
  }
  .min-h-\[115px\] {
    min-height: 115px;
  }
  .min-h-\[415px\] {
    min-height: 415px;
  }
  .min-h-\[521px\] {
    min-height: 521px;
  }
  .min-h-\[533px\] {
    min-height: 533px;
  }
  .min-h-\[calc\(100\%-56px\)\] {
    min-height: calc(100% - 56px);
  }
  .min-h-\[calc\(100vh-75px\)\] {
    min-height: calc(100vh - 75px);
  }
  .min-h-full {
    min-height: 100%;
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .\!w-2 {
    width: calc(var(--spacing) * 2) !important;
  }
  .\!w-2\.5 {
    width: calc(var(--spacing) * 2.5) !important;
  }
  .w-1 {
    width: calc(var(--spacing) * 1);
  }
  .w-1\/2 {
    width: calc(1/2 * 100%);
  }
  .w-1\/3 {
    width: calc(1/3 * 100%);
  }
  .w-3 {
    width: calc(var(--spacing) * 3);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-7 {
    width: calc(var(--spacing) * 7);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-9 {
    width: calc(var(--spacing) * 9);
  }
  .w-9\.5 {
    width: calc(var(--spacing) * 9.5);
  }
  .w-10 {
    width: calc(var(--spacing) * 10);
  }
  .w-11 {
    width: calc(var(--spacing) * 11);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-17 {
    width: calc(var(--spacing) * 17);
  }
  .w-17\.5 {
    width: calc(var(--spacing) * 17.5);
  }
  .w-20 {
    width: calc(var(--spacing) * 20);
  }
  .w-28 {
    width: calc(var(--spacing) * 28);
  }
  .w-32 {
    width: calc(var(--spacing) * 32);
  }
  .w-40 {
    width: calc(var(--spacing) * 40);
  }
  .w-44 {
    width: calc(var(--spacing) * 44);
  }
  .w-48 {
    width: calc(var(--spacing) * 48);
  }
  .w-56 {
    width: calc(var(--spacing) * 56);
  }
  .w-60 {
    width: calc(var(--spacing) * 60);
  }
  .w-65 {
    width: calc(var(--spacing) * 65);
  }
  .w-72 {
    width: calc(var(--spacing) * 72);
  }
  .w-79 {
    width: calc(var(--spacing) * 79);
  }
  .w-79\.5 {
    width: calc(var(--spacing) * 79.5);
  }
  .w-100 {
    width: calc(var(--spacing) * 100);
  }
  .w-\[17px\] {
    width: 17px;
  }
  .w-\[31px\] {
    width: 31px;
  }
  .w-\[calc\(100\%-32px\)\] {
    width: calc(100% - 32px);
  }
  .w-auto {
    width: auto;
  }
  .w-full {
    width: 100%;
  }
  .w-px {
    width: 1px;
  }
  .w-screen {
    width: 100vw;
  }
  .max-w-4xl {
    max-width: var(--container-4xl);
  }
  .max-w-5xl {
    max-width: var(--container-5xl);
  }
  .max-w-75 {
    max-width: calc(var(--spacing) * 75);
  }
  .max-w-full {
    max-width: 100%;
  }
  .max-w-lg {
    max-width: var(--container-lg);
  }
  .max-w-md {
    max-width: var(--container-md);
  }
  .max-w-none {
    max-width: none;
  }
  .max-w-screen {
    max-width: 100vw;
  }
  .max-w-sm {
    max-width: var(--container-sm);
  }
  .max-w-xl {
    max-width: var(--container-xl);
  }
  .max-w-xs {
    max-width: var(--container-xs);
  }
  .min-w-4 {
    min-width: calc(var(--spacing) * 4);
  }
  .min-w-4\.5 {
    min-width: calc(var(--spacing) * 4.5);
  }
  .min-w-9 {
    min-width: calc(var(--spacing) * 9);
  }
  .min-w-9\.5 {
    min-width: calc(var(--spacing) * 9.5);
  }
  .min-w-10 {
    min-width: calc(var(--spacing) * 10);
  }
  .min-w-20 {
    min-width: calc(var(--spacing) * 20);
  }
  .min-w-24 {
    min-width: calc(var(--spacing) * 24);
  }
  .min-w-36 {
    min-width: calc(var(--spacing) * 36);
  }
  .min-w-40 {
    min-width: calc(var(--spacing) * 40);
  }
  .min-w-45 {
    min-width: calc(var(--spacing) * 45);
  }
  .min-w-48 {
    min-width: calc(var(--spacing) * 48);
  }
  .min-w-70 {
    min-width: calc(var(--spacing) * 70);
  }
  .min-w-80 {
    min-width: calc(var(--spacing) * 80);
  }
  .min-w-full {
    min-width: 100%;
  }
  .min-w-screen {
    min-width: 100vw;
  }
  .flex-1 {
    flex: 1;
  }
  .flex-auto {
    flex: auto;
  }
  .flex-initial {
    flex: 0 auto;
  }
  .flex-none {
    flex: none;
  }
  .flex-shrink {
    flex-shrink: 1;
  }
  .flex-shrink-0 {
    flex-shrink: 0;
  }
  .shrink {
    flex-shrink: 1;
  }
  .shrink-0 {
    flex-shrink: 0;
  }
  .flex-grow {
    flex-grow: 1;
  }
  .flex-grow-1 {
    flex-grow: 1;
  }
  .grow {
    flex-grow: 1;
  }
  .basis-auto {
    flex-basis: auto;
  }
  .basis-full {
    flex-basis: 100%;
  }
  .table-auto {
    table-layout: auto;
  }
  .table-fixed {
    table-layout: fixed;
  }
  .caption-bottom {
    caption-side: bottom;
  }
  .caption-top {
    caption-side: top;
  }
  .border-collapse {
    border-collapse: collapse;
  }
  .border-separate {
    border-collapse: separate;
  }
  .origin-bottom {
    transform-origin: bottom;
  }
  .origin-bottom-left {
    transform-origin: bottom left;
  }
  .origin-bottom-right {
    transform-origin: bottom right;
  }
  .origin-center {
    transform-origin: center;
  }
  .origin-left {
    transform-origin: left;
  }
  .origin-right {
    transform-origin: right;
  }
  .origin-top {
    transform-origin: top;
  }
  .origin-top-left {
    transform-origin: top left;
  }
  .origin-top-right {
    transform-origin: top right;
  }
  .-translate-full {
    --tw-translate-x: -100%;
    --tw-translate-y: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-full {
    --tw-translate-x: 100%;
    --tw-translate-y: 100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-x-full {
    --tw-translate-x: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-x-full {
    --tw-translate-x: 100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-1 {
    --tw-translate-y: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-3d {
    translate: var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z);
  }
  .translate-none {
    translate: none;
  }
  .scale-3d {
    scale: var(--tw-scale-x) var(--tw-scale-y) var(--tw-scale-z);
  }
  .scale-none {
    scale: none;
  }
  .rotate-90 {
    rotate: 90deg;
  }
  .rotate-none {
    rotate: none;
  }
  .transform {
    transform: var(--tw-rotate-x) var(--tw-rotate-y) var(--tw-rotate-z) var(--tw-skew-x) var(--tw-skew-y);
  }
  .transform-cpu {
    transform: var(--tw-rotate-x) var(--tw-rotate-y) var(--tw-rotate-z) var(--tw-skew-x) var(--tw-skew-y);
  }
  .transform-gpu {
    transform: translateZ(0) var(--tw-rotate-x) var(--tw-rotate-y) var(--tw-rotate-z) var(--tw-skew-x) var(--tw-skew-y);
  }
  .transform-none {
    transform: none;
  }
  .animate-none {
    animation: none;
  }
  .animate-ping {
    animation: var(--animate-ping);
  }
  .animate-spin {
    animation: var(--animate-spin);
  }
  .cursor-not-allowed {
    cursor: not-allowed;
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .touch-pinch-zoom {
    --tw-pinch-zoom: pinch-zoom;
    touch-action: var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,);
  }
  .resize {
    resize: both;
  }
  .resize-none {
    resize: none;
  }
  .resize-x {
    resize: horizontal;
  }
  .resize-y {
    resize: vertical;
  }
  .snap-none {
    scroll-snap-type: none;
  }
  .snap-mandatory {
    --tw-scroll-snap-strictness: mandatory;
  }
  .snap-proximity {
    --tw-scroll-snap-strictness: proximity;
  }
  .snap-align-none {
    scroll-snap-align: none;
  }
  .snap-center {
    scroll-snap-align: center;
  }
  .snap-end {
    scroll-snap-align: end;
  }
  .snap-start {
    scroll-snap-align: start;
  }
  .snap-always {
    scroll-snap-stop: always;
  }
  .snap-normal {
    scroll-snap-stop: normal;
  }
  .list-inside {
    list-style-position: inside;
  }
  .list-outside {
    list-style-position: outside;
  }
  .list-decimal {
    list-style-type: decimal;
  }
  .list-disc {
    list-style-type: disc;
  }
  .list-none {
    list-style-type: none;
  }
  .list-image-none {
    list-style-image: none;
  }
  .form-select {
    appearance: none;
    background-color: #fff;
    border-color: oklch(0.551 0.027 264.364);
    border-width: 1px;
    border-radius: 0px;
    padding-top: 0.5rem;
    padding-right: 0.75rem;
    padding-bottom: 0.5rem;
    padding-left: 0.75rem;
    font-size: 1rem;
    line-height: 1.5rem;
    --tw-shadow: 0 0 #0000;
    &:focus {
      outline: 2px solid transparent;
      outline-offset: 2px;
      --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-color: oklch(0.546 0.245 262.881);
      --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
      --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
      box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      border-color: oklch(0.546 0.245 262.881);
    }
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='oklch(0.551 0.027 264.364)' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    print-color-adjust: exact;
    &:where([size]:not([size="1"])) {
      background-image: initial;
      background-position: initial;
      background-repeat: unset;
      background-size: initial;
      padding-right: 0.75rem;
      print-color-adjust: unset;
    }
  }
  .form-textarea {
    appearance: none;
    background-color: #fff;
    border-color: oklch(0.551 0.027 264.364);
    border-width: 1px;
    border-radius: 0px;
    padding-top: 0.5rem;
    padding-right: 0.75rem;
    padding-bottom: 0.5rem;
    padding-left: 0.75rem;
    font-size: 1rem;
    line-height: 1.5rem;
    --tw-shadow: 0 0 #0000;
    &:focus {
      outline: 2px solid transparent;
      outline-offset: 2px;
      --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-color: oklch(0.546 0.245 262.881);
      --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
      --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
      box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      border-color: oklch(0.546 0.245 262.881);
    }
    &::placeholder {
      color: oklch(0.551 0.027 264.364);
      opacity: 1;
    }
  }
  .form-multiselect {
    appearance: none;
    background-color: #fff;
    border-color: oklch(0.551 0.027 264.364);
    border-width: 1px;
    border-radius: 0px;
    padding-top: 0.5rem;
    padding-right: 0.75rem;
    padding-bottom: 0.5rem;
    padding-left: 0.75rem;
    font-size: 1rem;
    line-height: 1.5rem;
    --tw-shadow: 0 0 #0000;
    &:focus {
      outline: 2px solid transparent;
      outline-offset: 2px;
      --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-color: oklch(0.546 0.245 262.881);
      --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
      --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
      box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      border-color: oklch(0.546 0.245 262.881);
    }
  }
  .appearance-auto {
    appearance: auto;
  }
  .appearance-none {
    appearance: none;
  }
  .columns-1 {
    columns: 1;
  }
  .columns-auto {
    columns: auto;
  }
  .auto-cols-auto {
    grid-auto-columns: auto;
  }
  .auto-cols-fr {
    grid-auto-columns: minmax(0, 1fr);
  }
  .auto-cols-max {
    grid-auto-columns: max-content;
  }
  .auto-cols-min {
    grid-auto-columns: min-content;
  }
  .grid-flow-col {
    grid-auto-flow: column;
  }
  .grid-flow-col-dense {
    grid-auto-flow: column dense;
  }
  .grid-flow-dense {
    grid-auto-flow: dense;
  }
  .grid-flow-row {
    grid-auto-flow: row;
  }
  .grid-flow-row-dense {
    grid-auto-flow: row dense;
  }
  .auto-rows-auto {
    grid-auto-rows: auto;
  }
  .auto-rows-fr {
    grid-auto-rows: minmax(0, 1fr);
  }
  .auto-rows-max {
    grid-auto-rows: max-content;
  }
  .auto-rows-min {
    grid-auto-rows: min-content;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
  .grid-cols-none {
    grid-template-columns: none;
  }
  .grid-cols-subgrid {
    grid-template-columns: subgrid;
  }
  .grid-rows-none {
    grid-template-rows: none;
  }
  .grid-rows-subgrid {
    grid-template-rows: subgrid;
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-col-reverse {
    flex-direction: column-reverse;
  }
  .flex-row {
    flex-direction: row;
  }
  .flex-row-reverse {
    flex-direction: row-reverse;
  }
  .flex-nowrap {
    flex-wrap: nowrap;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .flex-wrap-reverse {
    flex-wrap: wrap-reverse;
  }
  .place-content-around {
    place-content: space-around;
  }
  .place-content-baseline {
    place-content: baseline;
  }
  .place-content-between {
    place-content: space-between;
  }
  .place-content-center {
    place-content: center;
  }
  .place-content-end {
    place-content: end;
  }
  .place-content-evenly {
    place-content: space-evenly;
  }
  .place-content-start {
    place-content: start;
  }
  .place-content-stretch {
    place-content: stretch;
  }
  .place-items-baseline {
    place-items: baseline;
  }
  .place-items-center {
    place-items: center;
  }
  .place-items-end {
    place-items: end;
  }
  .place-items-start {
    place-items: start;
  }
  .place-items-stretch {
    place-items: stretch;
  }
  .content-around {
    align-content: space-around;
  }
  .content-baseline {
    align-content: baseline;
  }
  .content-between {
    align-content: space-between;
  }
  .content-center {
    align-content: center;
  }
  .content-end {
    align-content: flex-end;
  }
  .content-evenly {
    align-content: space-evenly;
  }
  .content-normal {
    align-content: normal;
  }
  .content-start {
    align-content: flex-start;
  }
  .content-stretch {
    align-content: stretch;
  }
  .items-baseline {
    align-items: baseline;
  }
  .items-center {
    align-items: center;
  }
  .items-end {
    align-items: flex-end;
  }
  .items-start {
    align-items: flex-start;
  }
  .items-stretch {
    align-items: stretch;
  }
  .\!justify-between {
    justify-content: space-between !important;
  }
  .justify-around {
    justify-content: space-around;
  }
  .justify-baseline {
    justify-content: baseline;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .justify-evenly {
    justify-content: space-evenly;
  }
  .justify-normal {
    justify-content: normal;
  }
  .justify-start {
    justify-content: flex-start;
  }
  .justify-stretch {
    justify-content: stretch;
  }
  .justify-items-center {
    justify-items: center;
  }
  .justify-items-end {
    justify-items: end;
  }
  .justify-items-normal {
    justify-items: normal;
  }
  .justify-items-start {
    justify-items: start;
  }
  .justify-items-stretch {
    justify-items: stretch;
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }
  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-5 {
    gap: calc(var(--spacing) * 5);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .space-y-0 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-0\.5 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 0.5) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 0.5) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-1 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-2 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-3 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-5 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 5) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 5) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-8 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-reverse {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 1;
    }
  }
  .gap-x-1 {
    column-gap: calc(var(--spacing) * 1);
  }
  .gap-x-1\.5 {
    column-gap: calc(var(--spacing) * 1.5);
  }
  .gap-x-2 {
    column-gap: calc(var(--spacing) * 2);
  }
  .gap-x-3 {
    column-gap: calc(var(--spacing) * 3);
  }
  .gap-x-3\.5 {
    column-gap: calc(var(--spacing) * 3.5);
  }
  .gap-x-4 {
    column-gap: calc(var(--spacing) * 4);
  }
  .gap-x-5 {
    column-gap: calc(var(--spacing) * 5);
  }
  .-space-x-1 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * -1) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * -1) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .-space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * -2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * -2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-1 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-4 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-reverse {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 1;
    }
  }
  .gap-y-1 {
    row-gap: calc(var(--spacing) * 1);
  }
  .gap-y-2 {
    row-gap: calc(var(--spacing) * 2);
  }
  .divide-x {
    :where(& > :not(:last-child)) {
      --tw-divide-x-reverse: 0;
      border-inline-style: var(--tw-border-style);
      border-inline-start-width: calc(1px * var(--tw-divide-x-reverse));
      border-inline-end-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
    }
  }
  .divide-y {
    :where(& > :not(:last-child)) {
      --tw-divide-y-reverse: 0;
      border-bottom-style: var(--tw-border-style);
      border-top-style: var(--tw-border-style);
      border-top-width: calc(1px * var(--tw-divide-y-reverse));
      border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    }
  }
  .divide-y-reverse {
    :where(& > :not(:last-child)) {
      --tw-divide-y-reverse: 1;
    }
  }
  .divide-gray-200 {
    :where(& > :not(:last-child)) {
      border-color: var(--color-gray-200);
    }
  }
  .place-self-auto {
    place-self: auto;
  }
  .place-self-center {
    place-self: center;
  }
  .place-self-end {
    place-self: end;
  }
  .place-self-start {
    place-self: start;
  }
  .place-self-stretch {
    place-self: stretch;
  }
  .self-auto {
    align-self: auto;
  }
  .self-baseline {
    align-self: baseline;
  }
  .self-center {
    align-self: center;
  }
  .self-end {
    align-self: flex-end;
  }
  .self-start {
    align-self: flex-start;
  }
  .self-stretch {
    align-self: stretch;
  }
  .justify-self-auto {
    justify-self: auto;
  }
  .justify-self-center {
    justify-self: center;
  }
  .justify-self-end {
    justify-self: flex-end;
  }
  .justify-self-start {
    justify-self: flex-start;
  }
  .justify-self-stretch {
    justify-self: stretch;
  }
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-visible {
    overflow: visible;
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .overflow-x-hidden {
    overflow-x: hidden;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .scroll-auto {
    scroll-behavior: auto;
  }
  .scroll-smooth {
    scroll-behavior: smooth;
  }
  .\!rounded-xs {
    border-radius: var(--radius-xs) !important;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .rounded-sm {
    border-radius: var(--radius-sm);
  }
  .rounded-xl {
    border-radius: var(--radius-xl);
  }
  .rounded-xs {
    border-radius: var(--radius-xs);
  }
  .rounded-s {
    border-start-start-radius: 0.25rem;
    border-end-start-radius: 0.25rem;
  }
  .rounded-s-full {
    border-start-start-radius: calc(infinity * 1px);
    border-end-start-radius: calc(infinity * 1px);
  }
  .rounded-ss {
    border-start-start-radius: 0.25rem;
  }
  .rounded-e {
    border-start-end-radius: 0.25rem;
    border-end-end-radius: 0.25rem;
  }
  .rounded-e-full {
    border-start-end-radius: calc(infinity * 1px);
    border-end-end-radius: calc(infinity * 1px);
  }
  .rounded-e-md {
    border-start-end-radius: var(--radius-md);
    border-end-end-radius: var(--radius-md);
  }
  .rounded-se {
    border-start-end-radius: 0.25rem;
  }
  .rounded-ee {
    border-end-end-radius: 0.25rem;
  }
  .rounded-es {
    border-end-start-radius: 0.25rem;
  }
  .rounded-t {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
  }
  .rounded-t-lg {
    border-top-left-radius: var(--radius-lg);
    border-top-right-radius: var(--radius-lg);
  }
  .rounded-l {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
  }
  .rounded-tl {
    border-top-left-radius: 0.25rem;
  }
  .rounded-r {
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
  }
  .rounded-tr {
    border-top-right-radius: 0.25rem;
  }
  .rounded-b {
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
  }
  .rounded-b-lg {
    border-bottom-right-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
  }
  .rounded-b-xl {
    border-bottom-right-radius: var(--radius-xl);
    border-bottom-left-radius: var(--radius-xl);
  }
  .rounded-br {
    border-bottom-right-radius: 0.25rem;
  }
  .rounded-bl {
    border-bottom-left-radius: 0.25rem;
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-3 {
    border-style: var(--tw-border-style);
    border-width: 3px;
  }
  .border-\[1\.5px\] {
    border-style: var(--tw-border-style);
    border-width: 1.5px;
  }
  .border-\[3px\] {
    border-style: var(--tw-border-style);
    border-width: 3px;
  }
  .border-x {
    border-inline-style: var(--tw-border-style);
    border-inline-width: 1px;
  }
  .border-y {
    border-block-style: var(--tw-border-style);
    border-block-width: 1px;
  }
  .border-s {
    border-inline-start-style: var(--tw-border-style);
    border-inline-start-width: 1px;
  }
  .border-s-4 {
    border-inline-start-style: var(--tw-border-style);
    border-inline-start-width: 4px;
  }
  .border-e {
    border-inline-end-style: var(--tw-border-style);
    border-inline-end-width: 1px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }
  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }
  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }
  .border-dotted {
    --tw-border-style: dotted;
    border-style: dotted;
  }
  .border-double {
    --tw-border-style: double;
    border-style: double;
  }
  .border-hidden {
    --tw-border-style: hidden;
    border-style: hidden;
  }
  .border-none {
    --tw-border-style: none;
    border-style: none;
  }
  .border-solid {
    --tw-border-style: solid;
    border-style: solid;
  }
  .\!border-gray-200 {
    border-color: var(--color-gray-200) !important;
  }
  .\!border-neutral-700 {
    border-color: var(--color-neutral-700) !important;
  }
  .border-blue-600 {
    border-color: var(--color-blue-600);
  }
  .border-current {
    border-color: currentColor;
  }
  .border-gray-200 {
    border-color: var(--color-gray-200);
  }
  .border-gray-300 {
    border-color: var(--color-gray-300);
  }
  .border-red-200 {
    border-color: var(--color-red-200);
  }
  .border-red-500 {
    border-color: var(--color-red-500);
  }
  .border-transparent {
    border-color: transparent;
  }
  .border-white {
    border-color: var(--color-white);
  }
  .border-t-transparent {
    border-top-color: transparent;
  }
  .\!bg-neutral-800 {
    background-color: var(--color-neutral-800) !important;
  }
  .\!bg-white {
    background-color: var(--color-white) !important;
  }
  .bg-\(--my_variable\) {
    background-color: var(--my_variable);
  }
  .bg-\(color\:--my-color\) {
    background-color: var(--my-color);
  }
  .bg-\[\#0088cc\] {
    background-color: #0088cc;
  }
  .bg-\[color\:var\(--my-color\)\] {
    background-color: var(--my-color);
  }
  .bg-\[var\(--my_variable\)\] {
    background-color: var(--my_variable);
  }
  .bg-black {
    background-color: var(--color-black);
  }
  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }
  .bg-blue-400 {
    background-color: var(--color-blue-400);
  }
  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }
  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }
  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }
  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }
  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }
  .bg-gray-300 {
    background-color: var(--color-gray-300);
  }
  .bg-gray-800 {
    background-color: var(--color-gray-800);
  }
  .bg-gray-900 {
    background-color: var(--color-gray-900);
  }
  .bg-gray-900\/50 {
    background-color: color-mix(in oklab, var(--color-gray-900) 50%, transparent);
  }
  .bg-green-100 {
    background-color: var(--color-green-100);
  }
  .bg-indigo-600 {
    background-color: var(--color-indigo-600);
  }
  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }
  .bg-purple-500 {
    background-color: var(--color-purple-500);
  }
  .bg-purple-600 {
    background-color: var(--color-purple-600);
  }
  .bg-red-50 {
    background-color: var(--color-red-50);
  }
  .bg-red-100 {
    background-color: var(--color-red-100);
  }
  .bg-red-400 {
    background-color: var(--color-red-400);
  }
  .bg-red-500 {
    background-color: var(--color-red-500);
  }
  .bg-red-500\/50 {
    background-color: color-mix(in oklab, var(--color-red-500) 50%, transparent);
  }
  .bg-red-500\/\[50\%\] {
    background-color: color-mix(in oklab, var(--color-red-500) 50%, transparent);
  }
  .bg-teal-400 {
    background-color: var(--color-teal-400);
  }
  .bg-teal-500 {
    background-color: var(--color-teal-500);
  }
  .bg-transparent {
    background-color: transparent;
  }
  .bg-violet-200 {
    background-color: var(--color-violet-200);
  }
  .bg-violet-500 {
    background-color: var(--color-violet-500);
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-yellow-100 {
    background-color: var(--color-yellow-100);
  }
  .bg-yellow-500 {
    background-color: var(--color-yellow-500);
  }
  .-bg-conic {
    --tw-gradient-position: in oklab;
    background-image: conic-gradient(var(--tw-gradient-stops));
  }
  .bg-conic {
    --tw-gradient-position: in oklab;
    background-image: conic-gradient(var(--tw-gradient-stops));
  }
  .bg-linear-to-l {
    --tw-gradient-position: to left in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-linear-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-linear-to-tl {
    --tw-gradient-position: to top left in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-linear-to-tr {
    --tw-gradient-position: to top right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-radial {
    --tw-gradient-position: in oklab;
    background-image: radial-gradient(var(--tw-gradient-stops));
  }
  .bg-\[linear-gradient\(45deg\,var\(--color-gray-100\)_7\.14\%\,transparent_7\.14\%\,transparent_50\%\,var\(--color-gray-100\)_50\%\,var\(--color-gray-100\)_57\.14\%\,transparent_57\.14\%\,transparent\)\;\] {
    background-image: linear-gradient(45deg,var(--color-gray-100) 7.14%,transparent 7.14%,transparent 50%,var(--color-gray-100) 50%,var(--color-gray-100) 57.14%,transparent 57.14%,transparent);;
  }
  .bg-none {
    background-image: none;
  }
  .via-none {
    --tw-gradient-via-stops: initial;
  }
  .from-blue-500 {
    --tw-gradient-from: var(--color-blue-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-blue-600 {
    --tw-gradient-from: var(--color-blue-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-gray-100 {
    --tw-gradient-from: var(--color-gray-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-purple-600 {
    --tw-gradient-to: var(--color-purple-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-violet-500 {
    --tw-gradient-to: var(--color-violet-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-80\% {
    --tw-gradient-to-position: 80%;
  }
  .box-decoration-clone {
    -webkit-box-decoration-break: clone;
    box-decoration-break: clone;
  }
  .box-decoration-slice {
    -webkit-box-decoration-break: slice;
    box-decoration-break: slice;
  }
  .decoration-clone {
    -webkit-box-decoration-break: clone;
    box-decoration-break: clone;
  }
  .decoration-slice {
    -webkit-box-decoration-break: slice;
    box-decoration-break: slice;
  }
  .bg-\[length\:10px_10px\] {
    background-size: 10px 10px;
  }
  .bg-auto {
    background-size: auto;
  }
  .bg-contain {
    background-size: contain;
  }
  .bg-cover {
    background-size: cover;
  }
  .bg-fixed {
    background-attachment: fixed;
  }
  .bg-local {
    background-attachment: local;
  }
  .bg-scroll {
    background-attachment: scroll;
  }
  .bg-clip-border {
    background-clip: border-box;
  }
  .bg-clip-content {
    background-clip: content-box;
  }
  .bg-clip-padding {
    background-clip: padding-box;
  }
  .bg-clip-text {
    background-clip: text;
  }
  .bg-bottom {
    background-position: bottom;
  }
  .bg-center {
    background-position: center;
  }
  .bg-left {
    background-position: left;
  }
  .bg-left-bottom {
    background-position: left bottom;
  }
  .bg-left-top {
    background-position: left top;
  }
  .bg-right {
    background-position: right;
  }
  .bg-right-bottom {
    background-position: right bottom;
  }
  .bg-right-top {
    background-position: right top;
  }
  .bg-top {
    background-position: top;
  }
  .bg-no-repeat {
    background-repeat: no-repeat;
  }
  .bg-repeat {
    background-repeat: repeat;
  }
  .bg-repeat-round {
    background-repeat: round;
  }
  .bg-repeat-space {
    background-repeat: space;
  }
  .bg-repeat-x {
    background-repeat: repeat-x;
  }
  .bg-repeat-y {
    background-repeat: repeat-y;
  }
  .bg-origin-border {
    background-origin: border-box;
  }
  .bg-origin-content {
    background-origin: content-box;
  }
  .bg-origin-padding {
    background-origin: padding-box;
  }
  .fill-black {
    fill: var(--color-black);
  }
  .fill-blue-600 {
    fill: var(--color-blue-600);
  }
  .fill-gray-50 {
    fill: var(--color-gray-50);
  }
  .fill-gray-100 {
    fill: var(--color-gray-100);
  }
  .fill-gray-200 {
    fill: var(--color-gray-200);
  }
  .fill-none {
    fill: none;
  }
  .fill-red-500 {
    fill: var(--color-red-500);
  }
  .fill-white {
    fill: var(--color-white);
  }
  .stroke-blue-600 {
    stroke: var(--color-blue-600);
  }
  .stroke-gray-50 {
    stroke: var(--color-gray-50);
  }
  .stroke-gray-100 {
    stroke: var(--color-gray-100);
  }
  .stroke-gray-400 {
    stroke: var(--color-gray-400);
  }
  .stroke-none {
    stroke: none;
  }
  .object-contain {
    object-fit: contain;
  }
  .object-cover {
    object-fit: cover;
  }
  .object-fill {
    object-fit: fill;
  }
  .object-none {
    object-fit: none;
  }
  .object-scale-down {
    object-fit: scale-down;
  }
  .object-bottom {
    object-position: bottom;
  }
  .object-center {
    object-position: center;
  }
  .object-left {
    object-position: left;
  }
  .object-left-bottom {
    object-position: left bottom;
  }
  .object-left-top {
    object-position: left top;
  }
  .object-right {
    object-position: right;
  }
  .object-right-bottom {
    object-position: right bottom;
  }
  .object-right-top {
    object-position: right top;
  }
  .object-top {
    object-position: top;
  }
  .p-0 {
    padding: calc(var(--spacing) * 0);
  }
  .p-0\.5 {
    padding: calc(var(--spacing) * 0.5);
  }
  .p-1 {
    padding: calc(var(--spacing) * 1);
  }
  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-2\.5 {
    padding: calc(var(--spacing) * 2.5);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-5 {
    padding: calc(var(--spacing) * 5);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .p-11 {
    padding: calc(var(--spacing) * 11);
  }
  .p-12 {
    padding: calc(var(--spacing) * 12);
  }
  .p-55296 {
    padding: calc(var(--spacing) * 55296);
  }
  .px-0 {
    padding-inline: calc(var(--spacing) * 0);
  }
  .px-0\.5 {
    padding-inline: calc(var(--spacing) * 0.5);
  }
  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }
  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .px-7 {
    padding-inline: calc(var(--spacing) * 7);
  }
  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }
  .px-\[5px\] {
    padding-inline: 5px;
  }
  .\!py-0 {
    padding-block: calc(var(--spacing) * 0) !important;
  }
  .\!py-0\.5 {
    padding-block: calc(var(--spacing) * 0.5) !important;
  }
  .py-0 {
    padding-block: calc(var(--spacing) * 0);
  }
  .py-0\.5 {
    padding-block: calc(var(--spacing) * 0.5);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-3\.5 {
    padding-block: calc(var(--spacing) * 3.5);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-7 {
    padding-block: calc(var(--spacing) * 7);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .py-10 {
    padding-block: calc(var(--spacing) * 10);
  }
  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }
  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }
  .py-px {
    padding-block: 1px;
  }
  .ps-1 {
    padding-inline-start: calc(var(--spacing) * 1);
  }
  .ps-1\.5 {
    padding-inline-start: calc(var(--spacing) * 1.5);
  }
  .ps-2 {
    padding-inline-start: calc(var(--spacing) * 2);
  }
  .ps-2\.5 {
    padding-inline-start: calc(var(--spacing) * 2.5);
  }
  .ps-3 {
    padding-inline-start: calc(var(--spacing) * 3);
  }
  .ps-3\.5 {
    padding-inline-start: calc(var(--spacing) * 3.5);
  }
  .ps-5 {
    padding-inline-start: calc(var(--spacing) * 5);
  }
  .ps-7 {
    padding-inline-start: calc(var(--spacing) * 7);
  }
  .ps-9 {
    padding-inline-start: calc(var(--spacing) * 9);
  }
  .ps-10 {
    padding-inline-start: calc(var(--spacing) * 10);
  }
  .pe-0 {
    padding-inline-end: calc(var(--spacing) * 0);
  }
  .pe-1 {
    padding-inline-end: calc(var(--spacing) * 1);
  }
  .pe-2 {
    padding-inline-end: calc(var(--spacing) * 2);
  }
  .pe-3 {
    padding-inline-end: calc(var(--spacing) * 3);
  }
  .pe-4 {
    padding-inline-end: calc(var(--spacing) * 4);
  }
  .pe-5 {
    padding-inline-end: calc(var(--spacing) * 5);
  }
  .pe-6 {
    padding-inline-end: calc(var(--spacing) * 6);
  }
  .pe-8 {
    padding-inline-end: calc(var(--spacing) * 8);
  }
  .pe-9 {
    padding-inline-end: calc(var(--spacing) * 9);
  }
  .pe-16 {
    padding-inline-end: calc(var(--spacing) * 16);
  }
  .pe-20 {
    padding-inline-end: calc(var(--spacing) * 20);
  }
  .pe-24 {
    padding-inline-end: calc(var(--spacing) * 24);
  }
  .pe-\[5px\] {
    padding-inline-end: 5px;
  }
  .pt-0 {
    padding-top: calc(var(--spacing) * 0);
  }
  .pt-1 {
    padding-top: calc(var(--spacing) * 1);
  }
  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }
  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }
  .pt-5 {
    padding-top: calc(var(--spacing) * 5);
  }
  .pt-15 {
    padding-top: calc(var(--spacing) * 15);
  }
  .pr-10 {
    padding-right: calc(var(--spacing) * 10);
  }
  .pb-1 {
    padding-bottom: calc(var(--spacing) * 1);
  }
  .pb-1\.5 {
    padding-bottom: calc(var(--spacing) * 1.5);
  }
  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }
  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }
  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }
  .pb-5 {
    padding-bottom: calc(var(--spacing) * 5);
  }
  .pb-8 {
    padding-bottom: calc(var(--spacing) * 8);
  }
  .pb-10 {
    padding-bottom: calc(var(--spacing) * 10);
  }
  .pl-0 {
    padding-left: calc(var(--spacing) * 0);
  }
  .pl-0\.5 {
    padding-left: calc(var(--spacing) * 0.5);
  }
  .text-center {
    text-align: center;
  }
  .text-end {
    text-align: end;
  }
  .text-justify {
    text-align: justify;
  }
  .text-left {
    text-align: left;
  }
  .text-right {
    text-align: right;
  }
  .text-start {
    text-align: start;
  }
  .indent-1 {
    text-indent: calc(var(--spacing) * 1);
  }
  .align-baseline {
    vertical-align: baseline;
  }
  .align-bottom {
    vertical-align: bottom;
  }
  .align-middle {
    vertical-align: middle;
  }
  .align-sub {
    vertical-align: sub;
  }
  .align-super {
    vertical-align: super;
  }
  .align-text-bottom {
    vertical-align: text-bottom;
  }
  .align-text-top {
    vertical-align: text-top;
  }
  .align-top {
    vertical-align: top;
  }
  .font-mono {
    font-family: var(--font-mono);
  }
  .\!text-sm {
    font-size: var(--text-sm) !important;
    line-height: var(--tw-leading, var(--text-sm--line-height)) !important;
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
  .text-5xl {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .text-sm\/6 {
    font-size: var(--text-sm);
    line-height: calc(var(--spacing) * 6);
  }
  .text-\[10px\] {
    font-size: 10px;
  }
  .text-\[11px\] {
    font-size: 11px;
  }
  .text-\[13px\] {
    font-size: 13px;
  }
  .leading-3 {
    --tw-leading: calc(var(--spacing) * 3);
    line-height: calc(var(--spacing) * 3);
  }
  .leading-4 {
    --tw-leading: calc(var(--spacing) * 4);
    line-height: calc(var(--spacing) * 4);
  }
  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }
  .\!font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium) !important;
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .text-balance {
    text-wrap: balance;
  }
  .text-nowrap {
    text-wrap: nowrap;
  }
  .text-pretty {
    text-wrap: pretty;
  }
  .text-wrap {
    text-wrap: wrap;
  }
  .break-normal {
    overflow-wrap: normal;
    word-break: normal;
  }
  .break-words {
    overflow-wrap: break-word;
  }
  .break-all {
    word-break: break-all;
  }
  .break-keep {
    word-break: keep-all;
  }
  .overflow-ellipsis {
    text-overflow: ellipsis;
  }
  .text-clip {
    text-overflow: clip;
  }
  .text-ellipsis {
    text-overflow: ellipsis;
  }
  .hyphens-auto {
    -webkit-hyphens: auto;
    hyphens: auto;
  }
  .hyphens-manual {
    -webkit-hyphens: manual;
    hyphens: manual;
  }
  .hyphens-none {
    -webkit-hyphens: none;
    hyphens: none;
  }
  .whitespace-break-spaces {
    white-space: break-spaces;
  }
  .whitespace-normal {
    white-space: normal;
  }
  .whitespace-nowrap {
    white-space: nowrap;
  }
  .whitespace-pre {
    white-space: pre;
  }
  .whitespace-pre-line {
    white-space: pre-line;
  }
  .whitespace-pre-wrap {
    white-space: pre-wrap;
  }
  .\[color\:red\] {
    color: red;
  }
  .\[color\:red\]\/50 {
    color: color-mix(in oklab, red 50%, transparent);
  }
  .\[color\:red\]\/50\! {
    color: color-mix(in oklab, red 50%, transparent) !important;
  }
  .text-black {
    color: var(--color-black);
  }
  .text-blue-500 {
    color: var(--color-blue-500);
  }
  .text-blue-600 {
    color: var(--color-blue-600);
  }
  .text-blue-800 {
    color: var(--color-blue-800);
  }
  .text-gray-300 {
    color: var(--color-gray-300);
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-600 {
    color: var(--color-gray-600);
  }
  .text-gray-700 {
    color: var(--color-gray-700);
  }
  .text-gray-800 {
    color: var(--color-gray-800);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .text-green-600 {
    color: var(--color-green-600);
  }
  .text-green-800 {
    color: var(--color-green-800);
  }
  .text-indigo-600 {
    color: var(--color-indigo-600);
  }
  .text-neutral-500 {
    color: var(--color-neutral-500);
  }
  .text-pink-500 {
    color: var(--color-pink-500);
  }
  .text-purple-800 {
    color: var(--color-purple-800);
  }
  .text-red-400 {
    color: var(--color-red-400);
  }
  .text-red-500 {
    color: var(--color-red-500);
  }
  .text-red-600 {
    color: var(--color-red-600);
  }
  .text-red-800 {
    color: var(--color-red-800);
  }
  .text-teal-500 {
    color: var(--color-teal-500);
  }
  .text-teal-600 {
    color: var(--color-teal-600);
  }
  .text-transparent {
    color: transparent;
  }
  .text-violet-800 {
    color: var(--color-violet-800);
  }
  .text-white {
    color: var(--color-white);
  }
  .text-yellow-500 {
    color: var(--color-yellow-500);
  }
  .text-yellow-800 {
    color: var(--color-yellow-800);
  }
  .capitalize {
    text-transform: capitalize;
  }
  .lowercase {
    text-transform: lowercase;
  }
  .normal-case {
    text-transform: none;
  }
  .uppercase {
    text-transform: uppercase;
  }
  .\!italic {
    font-style: italic !important;
  }
  .italic {
    font-style: italic;
  }
  .not-italic {
    font-style: normal;
  }
  .font-stretch-condensed {
    font-stretch: condensed;
  }
  .font-stretch-expanded {
    font-stretch: expanded;
  }
  .font-stretch-extra-condensed {
    font-stretch: extra-condensed;
  }
  .font-stretch-extra-expanded {
    font-stretch: extra-expanded;
  }
  .font-stretch-normal {
    font-stretch: normal;
  }
  .font-stretch-semi-condensed {
    font-stretch: semi-condensed;
  }
  .font-stretch-semi-expanded {
    font-stretch: semi-expanded;
  }
  .font-stretch-ultra-condensed {
    font-stretch: ultra-condensed;
  }
  .font-stretch-ultra-expanded {
    font-stretch: ultra-expanded;
  }
  .diagonal-fractions {
    --tw-numeric-fraction: diagonal-fractions;
    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);
  }
  .lining-nums {
    --tw-numeric-figure: lining-nums;
    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);
  }
  .oldstyle-nums {
    --tw-numeric-figure: oldstyle-nums;
    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);
  }
  .ordinal {
    --tw-ordinal: ordinal;
    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);
  }
  .proportional-nums {
    --tw-numeric-spacing: proportional-nums;
    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);
  }
  .slashed-zero {
    --tw-slashed-zero: slashed-zero;
    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);
  }
  .stacked-fractions {
    --tw-numeric-fraction: stacked-fractions;
    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);
  }
  .tabular-nums {
    --tw-numeric-spacing: tabular-nums;
    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);
  }
  .normal-nums {
    font-variant-numeric: normal;
  }
  .line-through {
    text-decoration-line: line-through;
  }
  .no-underline {
    text-decoration-line: none;
  }
  .overline {
    text-decoration-line: overline;
  }
  .underline {
    text-decoration-line: underline;
  }
  .decoration-dashed {
    text-decoration-style: dashed;
  }
  .decoration-dotted {
    text-decoration-style: dotted;
  }
  .decoration-double {
    text-decoration-style: double;
  }
  .decoration-solid {
    text-decoration-style: solid;
  }
  .decoration-wavy {
    text-decoration-style: wavy;
  }
  .decoration-2 {
    text-decoration-thickness: 2px;
  }
  .decoration-auto {
    text-decoration-thickness: auto;
  }
  .decoration-from-font {
    text-decoration-thickness: from-font;
  }
  .underline-offset-4 {
    text-underline-offset: 4px;
  }
  .underline-offset-auto {
    text-underline-offset: auto;
  }
  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .subpixel-antialiased {
    -webkit-font-smoothing: auto;
    -moz-osx-font-smoothing: auto;
  }
  .accent-auto {
    accent-color: auto;
  }
  .scheme-dark {
    color-scheme: dark;
  }
  .scheme-light {
    color-scheme: light;
  }
  .scheme-light-dark {
    color-scheme: light dark;
  }
  .scheme-normal {
    color-scheme: normal;
  }
  .scheme-only-dark {
    color-scheme: only dark;
  }
  .scheme-only-light {
    color-scheme: only light;
  }
  .opacity-0 {
    opacity: 0%;
  }
  .opacity-50 {
    opacity: 50%;
  }
  .opacity-75 {
    opacity: 75%;
  }
  .opacity-100 {
    opacity: 100%;
  }
  .mix-blend-plus-darker {
    mix-blend-mode: plus-darker;
  }
  .mix-blend-plus-lighter {
    mix-blend-mode: plus-lighter;
  }
  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-2xs {
    --tw-shadow: 0 1px var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xs {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentColor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-2 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentColor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .inset-ring {
    --tw-inset-ring-shadow: inset 0 0 0 1px var(--tw-inset-ring-color, currentColor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-initial {
    --tw-shadow-color: initial;
  }
  .ring-white {
    --tw-ring-color: var(--color-white);
  }
  .inset-shadow-initial {
    --tw-inset-shadow-color: initial;
  }
  .outline-hidden {
    outline-style: none;
    @media (forced-colors: active) {
      outline: 2px solid transparent;
      outline-offset: 2px;
    }
  }
  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .blur-none {
    --tw-blur:  ;
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .drop-shadow {
    --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow( 0 1px 1px rgb(0 0 0 / 0.06));
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .drop-shadow-none {
    --tw-drop-shadow:  ;
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .grayscale {
    --tw-grayscale: grayscale(100%);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .hue-rotate-180 {
    --tw-hue-rotate: hue-rotate(180deg);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .\!invert {
    --tw-invert: invert(100%);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,) !important;
  }
  .invert {
    --tw-invert: invert(100%);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .sepia {
    --tw-sepia: sepia(100%);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .\!filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,) !important;
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .backdrop-blur {
    --tw-backdrop-blur: blur(8px);
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .backdrop-blur-none {
    --tw-backdrop-blur:  ;
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .backdrop-grayscale {
    --tw-backdrop-grayscale: grayscale(100%);
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .backdrop-invert {
    --tw-backdrop-invert: invert(100%);
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .backdrop-sepia {
    --tw-backdrop-sepia: sepia(100%);
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .backdrop-filter {
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .\!transition-transform {
    transition-property: transform, translate, scale, rotate !important;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function)) !important;
    transition-duration: var(--tw-duration, var(--default-transition-duration)) !important;
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-\[height\] {
    transition-property: height;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-\[opacity\,margin\] {
    transition-property: opacity,margin;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-none {
    transition-property: none;
  }
  .transition-discrete {
    transition-behavior: allow-discrete;
  }
  .transition-normal {
    transition-behavior: normal;
  }
  .duration-200 {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .duration-300 {
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .duration-500 {
    --tw-duration: 500ms;
    transition-duration: 500ms;
  }
  .duration-\[0\.1ms\] {
    --tw-duration: 0.1ms;
    transition-duration: 0.1ms;
  }
  .ease-in {
    --tw-ease: var(--ease-in);
    transition-timing-function: var(--ease-in);
  }
  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
  .ease-linear {
    --tw-ease: linear;
    transition-timing-function: linear;
  }
  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }
  .will-change-auto {
    will-change: auto;
  }
  .will-change-contents {
    will-change: contents;
  }
  .will-change-scroll {
    will-change: scroll-position;
  }
  .will-change-transform {
    will-change: transform;
  }
  .contain-inline-size {
    --tw-contain-size: inline-size;
    contain: var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,);
  }
  .contain-layout {
    --tw-contain-layout: layout;
    contain: var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,);
  }
  .contain-paint {
    --tw-contain-paint: paint;
    contain: var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,);
  }
  .contain-size {
    --tw-contain-size: size;
    contain: var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,);
  }
  .contain-style {
    --tw-contain-style: style;
    contain: var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,);
  }
  .contain-content {
    contain: content;
  }
  .contain-none {
    contain: none;
  }
  .contain-strict {
    contain: strict;
  }
  .content-none {
    --tw-content: none;
    content: none;
  }
  .forced-color-adjust-auto {
    forced-color-adjust: auto;
  }
  .forced-color-adjust-none {
    forced-color-adjust: none;
  }
  .outline-dashed {
    --tw-outline-style: dashed;
    outline-style: dashed;
  }
  .outline-dotted {
    --tw-outline-style: dotted;
    outline-style: dotted;
  }
  .outline-double {
    --tw-outline-style: double;
    outline-style: double;
  }
  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }
  .outline-solid {
    --tw-outline-style: solid;
    outline-style: solid;
  }
  .select-all {
    -webkit-user-select: all;
    user-select: all;
  }
  .\[--adaptive\:none\] {
    --adaptive: none;
  }
  .\[--auto-close\:inside\] {
    --auto-close: inside;
  }
  .\[--auto-close\:lg\] {
    --auto-close: lg;
  }
  .\[--auto-close\:true\] {
    --auto-close: true;
  }
  .\[--close-when-click-inside\:true\] {
    --close-when-click-inside: true;
  }
  .\[--flip\:false\] {
    --flip: false;
  }
  .\[--is-toggle-tooltip\:false\] {
    --is-toggle-tooltip: false;
  }
  .\[--placement\:bottom-right\] {
    --placement: bottom-right;
  }
  .\[--placement\:bottom\] {
    --placement: bottom;
  }
  .\[--placement\:top-right\] {
    --placement: top-right;
  }
  .\[--strategy\:absolute\] {
    --strategy: absolute;
  }
  .\[--strategy\:static\] {
    --strategy: static;
  }
  .\[keywords\:node-addon-api\] {
    keywords: node-addon-api;
  }
  .backface-hidden {
    backface-visibility: hidden;
  }
  .backface-visible {
    backface-visibility: visible;
  }
  .divide-x-reverse {
    :where(& > :not(:last-child)) {
      --tw-divide-x-reverse: 1;
    }
  }
  .duration-initial {
    --tw-duration: initial;
  }
  .ease-initial {
    --tw-ease: initial;
  }
  .perspective-none {
    perspective: none;
  }
  .perspective-origin-bottom {
    perspective-origin: bottom;
  }
  .perspective-origin-bottom-left {
    perspective-origin: bottom left;
  }
  .perspective-origin-bottom-right {
    perspective-origin: bottom right;
  }
  .perspective-origin-center {
    perspective-origin: center;
  }
  .perspective-origin-left {
    perspective-origin: left;
  }
  .perspective-origin-right {
    perspective-origin: right;
  }
  .perspective-origin-top {
    perspective-origin: top;
  }
  .perspective-origin-top-left {
    perspective-origin: top left;
  }
  .perspective-origin-top-right {
    perspective-origin: top right;
  }
  .ring-inset {
    --tw-ring-inset: inset;
  }
  .transform-3d {
    transform-style: preserve-3d;
  }
  .transform-border {
    transform-box: border-box;
  }
  .transform-content {
    transform-box: content-box;
  }
  .transform-fill {
    transform-box: fill-box;
  }
  .transform-flat {
    transform-style: flat;
  }
  .transform-stroke {
    transform-box: stroke-box;
  }
  .transform-view {
    transform-box: view-box;
  }
  .group-hover\:translate-x-1 {
    &:is(:where(.group):hover *) {
      --tw-translate-x: calc(var(--spacing) * 1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .group-hover\:text-blue-600 {
    &:is(:where(.group):hover *) {
      color: var(--color-blue-600);
    }
  }
  .group-focus\:translate-x-1 {
    &:is(:where(.group):focus *) {
      --tw-translate-x: calc(var(--spacing) * 1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .group-focus\:text-blue-600 {
    &:is(:where(.group):focus *) {
      color: var(--color-blue-600);
    }
  }
  .peer-checked\:translate-x-full {
    &:is(:where(.peer):checked ~ *) {
      --tw-translate-x: 100%;
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .peer-checked\:bg-blue-600 {
    &:is(:where(.peer):checked ~ *) {
      background-color: var(--color-blue-600);
    }
  }
  .peer-disabled\:pointer-events-none {
    &:is(:where(.peer):disabled ~ *) {
      pointer-events: none;
    }
  }
  .peer-disabled\:opacity-50 {
    &:is(:where(.peer):disabled ~ *) {
      opacity: 50%;
    }
  }
  .placeholder\:text-gray-400 {
    &::placeholder {
      color: var(--color-gray-400);
    }
  }
  .placeholder\:text-gray-500 {
    &::placeholder {
      color: var(--color-gray-500);
    }
  }
  .before\:absolute {
    &::before {
      content: var(--tw-content);
      position: absolute;
    }
  }
  .before\:inset-0 {
    &::before {
      content: var(--tw-content);
      inset: calc(var(--spacing) * 0);
    }
  }
  .before\:start-0 {
    &::before {
      content: var(--tw-content);
      inset-inline-start: calc(var(--spacing) * 0);
    }
  }
  .before\:start-4\.5 {
    &::before {
      content: var(--tw-content);
      inset-inline-start: calc(var(--spacing) * 4.5);
    }
  }
  .before\:start-\[19px\] {
    &::before {
      content: var(--tw-content);
      inset-inline-start: 19px;
    }
  }
  .before\:-end-5 {
    &::before {
      content: var(--tw-content);
      inset-inline-end: calc(var(--spacing) * -5);
    }
  }
  .before\:end-2 {
    &::before {
      content: var(--tw-content);
      inset-inline-end: calc(var(--spacing) * 2);
    }
  }
  .before\:top-0 {
    &::before {
      content: var(--tw-content);
      top: calc(var(--spacing) * 0);
    }
  }
  .before\:top-1\/2 {
    &::before {
      content: var(--tw-content);
      top: calc(1/2 * 100%);
    }
  }
  .before\:-z-1 {
    &::before {
      content: var(--tw-content);
      z-index: calc(1 * -1);
    }
  }
  .before\:z-1 {
    &::before {
      content: var(--tw-content);
      z-index: 1;
    }
  }
  .before\:size-full {
    &::before {
      content: var(--tw-content);
      width: 100%;
      height: 100%;
    }
  }
  .before\:h-3\.5 {
    &::before {
      content: var(--tw-content);
      height: calc(var(--spacing) * 3.5);
    }
  }
  .before\:h-full {
    &::before {
      content: var(--tw-content);
      height: 100%;
    }
  }
  .before\:w-0\.5 {
    &::before {
      content: var(--tw-content);
      width: calc(var(--spacing) * 0.5);
    }
  }
  .before\:w-5 {
    &::before {
      content: var(--tw-content);
      width: calc(var(--spacing) * 5);
    }
  }
  .before\:w-px {
    &::before {
      content: var(--tw-content);
      width: 1px;
    }
  }
  .before\:-translate-y-1\/2 {
    &::before {
      content: var(--tw-content);
      --tw-translate-y: calc(calc(1/2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .before\:rotate-\[18deg\] {
    &::before {
      content: var(--tw-content);
      rotate: 18deg;
    }
  }
  .before\:bg-gray-100 {
    &::before {
      content: var(--tw-content);
      background-color: var(--color-gray-100);
    }
  }
  .before\:bg-gray-400 {
    &::before {
      content: var(--tw-content);
      background-color: var(--color-gray-400);
    }
  }
  .before\:bg-\[url\(\'https\:\/\/preline\.co\/assets\/svg\/component\/hero-gradient\.svg\'\)\] {
    &::before {
      content: var(--tw-content);
      background-image: url('https://preline.co/assets/svg/component/hero-gradient.svg');
    }
  }
  .before\:bg-cover {
    &::before {
      content: var(--tw-content);
      background-size: cover;
    }
  }
  .before\:bg-center {
    &::before {
      content: var(--tw-content);
      background-position: center;
    }
  }
  .before\:bg-no-repeat {
    &::before {
      content: var(--tw-content);
      background-repeat: no-repeat;
    }
  }
  .after\:pointer-events-none {
    &::after {
      content: var(--tw-content);
      pointer-events: none;
    }
  }
  .after\:absolute {
    &::after {
      content: var(--tw-content);
      position: absolute;
    }
  }
  .after\:inset-x-2 {
    &::after {
      content: var(--tw-content);
      inset-inline: calc(var(--spacing) * 2);
    }
  }
  .after\:start-\[19px\] {
    &::after {
      content: var(--tw-content);
      inset-inline-start: 19px;
    }
  }
  .after\:top-9\.5 {
    &::after {
      content: var(--tw-content);
      top: calc(var(--spacing) * 9.5);
    }
  }
  .after\:-bottom-2 {
    &::after {
      content: var(--tw-content);
      bottom: calc(var(--spacing) * -2);
    }
  }
  .after\:bottom-0 {
    &::after {
      content: var(--tw-content);
      bottom: calc(var(--spacing) * 0);
    }
  }
  .after\:z-10 {
    &::after {
      content: var(--tw-content);
      z-index: 10;
    }
  }
  .after\:h-0\.5 {
    &::after {
      content: var(--tw-content);
      height: calc(var(--spacing) * 0.5);
    }
  }
  .after\:w-px {
    &::after {
      content: var(--tw-content);
      width: 1px;
    }
  }
  .after\:bg-gray-200 {
    &::after {
      content: var(--tw-content);
      background-color: var(--color-gray-200);
    }
  }
  .first\:rounded-s-full {
    &:first-child {
      border-start-start-radius: calc(infinity * 1px);
      border-end-start-radius: calc(infinity * 1px);
    }
  }
  .first\:border-transparent {
    &:first-child {
      border-color: transparent;
    }
  }
  .first\:pt-0 {
    &:first-child {
      padding-top: calc(var(--spacing) * 0);
    }
  }
  .last\:mb-0 {
    &:last-child {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .last\:rounded-e-full {
    &:last-child {
      border-start-end-radius: calc(infinity * 1px);
      border-end-end-radius: calc(infinity * 1px);
    }
  }
  .last\:border-0 {
    &:last-child {
      border-style: var(--tw-border-style);
      border-width: 0px;
    }
  }
  .last\:border-b-0 {
    &:last-child {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 0px;
    }
  }
  .last\:border-b-transparent {
    &:last-child {
      border-bottom-color: transparent;
    }
  }
  .last\:pe-0 {
    &:last-child {
      padding-inline-end: calc(var(--spacing) * 0);
    }
  }
  .last\:pb-0 {
    &:last-child {
      padding-bottom: calc(var(--spacing) * 0);
    }
  }
  .last\:after\:hidden {
    &:last-child {
      &::after {
        content: var(--tw-content);
        display: none;
      }
    }
  }
  .last-of-type\:before\:hidden {
    &:last-of-type {
      &::before {
        content: var(--tw-content);
        display: none;
      }
    }
  }
  .checked\:border-blue-500 {
    &:checked {
      border-color: var(--color-blue-500);
    }
  }
  .checked\:border-indigo-600 {
    &:checked {
      border-color: var(--color-indigo-600);
    }
  }
  .focus-within\:ring-2 {
    &:focus-within {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentColor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus-within\:ring-blue-600 {
    &:focus-within {
      --tw-ring-color: var(--color-blue-600);
    }
  }
  .focus-within\:ring-offset-2 {
    &:focus-within {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus-within\:outline-hidden {
    &:focus-within {
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
  }
  .hover\:z-10 {
    &:hover {
      z-index: 10;
    }
  }
  .hover\:border-blue-600 {
    &:hover {
      border-color: var(--color-blue-600);
    }
  }
  .hover\:border-gray-200 {
    &:hover {
      border-color: var(--color-gray-200);
    }
  }
  .hover\:bg-blue-700 {
    &:hover {
      background-color: var(--color-blue-700);
    }
  }
  .hover\:bg-gray-50 {
    &:hover {
      background-color: var(--color-gray-50);
    }
  }
  .hover\:bg-gray-100 {
    &:hover {
      background-color: var(--color-gray-100);
    }
  }
  .hover\:bg-gray-200 {
    &:hover {
      background-color: var(--color-gray-200);
    }
  }
  .hover\:bg-indigo-700 {
    &:hover {
      background-color: var(--color-indigo-700);
    }
  }
  .hover\:bg-red-100 {
    &:hover {
      background-color: var(--color-red-100);
    }
  }
  .hover\:text-blue-600 {
    &:hover {
      color: var(--color-blue-600);
    }
  }
  .hover\:text-blue-700 {
    &:hover {
      color: var(--color-blue-700);
    }
  }
  .hover\:text-gray-600 {
    &:hover {
      color: var(--color-gray-600);
    }
  }
  .hover\:text-gray-800 {
    &:hover {
      color: var(--color-gray-800);
    }
  }
  .hover\:text-gray-900 {
    &:hover {
      color: var(--color-gray-900);
    }
  }
  .hover\:text-indigo-600 {
    &:hover {
      color: var(--color-indigo-600);
    }
  }
  .hover\:underline {
    &:hover {
      text-decoration-line: underline;
    }
  }
  .hover\:opacity-100 {
    &:hover {
      opacity: 100%;
    }
  }
  .hover\:shadow-2xs {
    &:hover {
      --tw-shadow: 0 1px var(--tw-shadow-color, rgb(0 0 0 / 0.05));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:z-10 {
    &:focus {
      z-index: 10;
    }
  }
  .focus\:border-black {
    &:focus {
      border-color: var(--color-black);
    }
  }
  .focus\:border-blue-500 {
    &:focus {
      border-color: var(--color-blue-500);
    }
  }
  .focus\:border-blue-600 {
    &:focus {
      border-color: var(--color-blue-600);
    }
  }
  .focus\:border-gray-300 {
    &:focus {
      border-color: var(--color-gray-300);
    }
  }
  .focus\:border-gray-500 {
    &:focus {
      border-color: var(--color-gray-500);
    }
  }
  .focus\:border-indigo-300 {
    &:focus {
      border-color: var(--color-indigo-300);
    }
  }
  .focus\:border-indigo-500 {
    &:focus {
      border-color: var(--color-indigo-500);
    }
  }
  .focus\:border-transparent {
    &:focus {
      border-color: transparent;
    }
  }
  .focus\:bg-blue-700 {
    &:focus {
      background-color: var(--color-blue-700);
    }
  }
  .focus\:bg-gray-50 {
    &:focus {
      background-color: var(--color-gray-50);
    }
  }
  .focus\:bg-gray-100 {
    &:focus {
      background-color: var(--color-gray-100);
    }
  }
  .focus\:bg-gray-200 {
    &:focus {
      background-color: var(--color-gray-200);
    }
  }
  .focus\:bg-indigo-700 {
    &:focus {
      background-color: var(--color-indigo-700);
    }
  }
  .focus\:bg-red-100 {
    &:focus {
      background-color: var(--color-red-100);
    }
  }
  .focus\:bg-white {
    &:focus {
      background-color: var(--color-white);
    }
  }
  .focus\:text-blue-600 {
    &:focus {
      color: var(--color-blue-600);
    }
  }
  .focus\:text-blue-700 {
    &:focus {
      color: var(--color-blue-700);
    }
  }
  .focus\:text-gray-600 {
    &:focus {
      color: var(--color-gray-600);
    }
  }
  .focus\:text-gray-800 {
    &:focus {
      color: var(--color-gray-800);
    }
  }
  .focus\:text-indigo-600 {
    &:focus {
      color: var(--color-indigo-600);
    }
  }
  .focus\:underline {
    &:focus {
      text-decoration-line: underline;
    }
  }
  .focus\:decoration-2 {
    &:focus {
      text-decoration-thickness: 2px;
    }
  }
  .focus\:opacity-80 {
    &:focus {
      opacity: 80%;
    }
  }
  .focus\:opacity-100 {
    &:focus {
      opacity: 100%;
    }
  }
  .focus\:ring {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentColor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-0 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentColor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-1 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentColor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-2 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentColor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-black {
    &:focus {
      --tw-ring-color: var(--color-black);
    }
  }
  .focus\:ring-blue-300 {
    &:focus {
      --tw-ring-color: var(--color-blue-300);
    }
  }
  .focus\:ring-blue-500 {
    &:focus {
      --tw-ring-color: var(--color-blue-500);
    }
  }
  .focus\:ring-blue-600 {
    &:focus {
      --tw-ring-color: var(--color-blue-600);
    }
  }
  .focus\:ring-gray-300 {
    &:focus {
      --tw-ring-color: var(--color-gray-300);
    }
  }
  .focus\:ring-gray-500 {
    &:focus {
      --tw-ring-color: var(--color-gray-500);
    }
  }
  .focus\:ring-indigo-200 {
    &:focus {
      --tw-ring-color: var(--color-indigo-200);
    }
  }
  .focus\:ring-indigo-500 {
    &:focus {
      --tw-ring-color: var(--color-indigo-500);
    }
  }
  .focus\:ring-indigo-600 {
    &:focus {
      --tw-ring-color: var(--color-indigo-600);
    }
  }
  .focus\:ring-offset-0 {
    &:focus {
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus\:ring-offset-2 {
    &:focus {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus\:outline-hidden {
    &:focus {
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
  }
  .focus\:outline-none {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .disabled\:pointer-events-none {
    &:disabled {
      pointer-events: none;
    }
  }
  .disabled\:opacity-50 {
    &:disabled {
      opacity: 50%;
    }
  }
  .disabled\:placeholder\:text-neutral-600 {
    &:disabled {
      &::placeholder {
        color: var(--color-neutral-600);
      }
    }
  }
  .has-checked\:bg-white {
    &:has(*:checked) {
      background-color: var(--color-white);
    }
  }
  .has-checked\:shadow-2xs {
    &:has(*:checked) {
      --tw-shadow: 0 1px var(--tw-shadow-color, rgb(0 0 0 / 0.05));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .sm\:absolute {
    @media (width >= 40rem) {
      position: absolute;
    }
  }
  .sm\:end-5 {
    @media (width >= 40rem) {
      inset-inline-end: calc(var(--spacing) * 5);
    }
  }
  .sm\:top-5 {
    @media (width >= 40rem) {
      top: calc(var(--spacing) * 5);
    }
  }
  .sm\:mx-auto {
    @media (width >= 40rem) {
      margin-inline: auto;
    }
  }
  .sm\:mt-0 {
    @media (width >= 40rem) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .sm\:mb-6 {
    @media (width >= 40rem) {
      margin-bottom: calc(var(--spacing) * 6);
    }
  }
  .sm\:block {
    @media (width >= 40rem) {
      display: block;
    }
  }
  .sm\:flex {
    @media (width >= 40rem) {
      display: flex;
    }
  }
  .sm\:hidden {
    @media (width >= 40rem) {
      display: none;
    }
  }
  .sm\:size-4 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
    }
  }
  .sm\:h-16 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 16);
    }
  }
  .sm\:min-h-\[calc\(100vh-100px\)\] {
    @media (width >= 40rem) {
      min-height: calc(100vh - 100px);
    }
  }
  .sm\:w-80 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 80);
    }
  }
  .sm\:w-96 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 96);
    }
  }
  .sm\:w-100 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 100);
    }
  }
  .sm\:w-120 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 120);
    }
  }
  .sm\:w-159 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 159);
    }
  }
  .sm\:w-full {
    @media (width >= 40rem) {
      width: 100%;
    }
  }
  .sm\:max-w-lg {
    @media (width >= 40rem) {
      max-width: var(--container-lg);
    }
  }
  .sm\:max-w-xl {
    @media (width >= 40rem) {
      max-width: var(--container-xl);
    }
  }
  .sm\:flex-row {
    @media (width >= 40rem) {
      flex-direction: row;
    }
  }
  .sm\:items-center {
    @media (width >= 40rem) {
      align-items: center;
    }
  }
  .sm\:justify-between {
    @media (width >= 40rem) {
      justify-content: space-between;
    }
  }
  .sm\:justify-end {
    @media (width >= 40rem) {
      justify-content: flex-end;
    }
  }
  .sm\:gap-2 {
    @media (width >= 40rem) {
      gap: calc(var(--spacing) * 2);
    }
  }
  .sm\:gap-5 {
    @media (width >= 40rem) {
      gap: calc(var(--spacing) * 5);
    }
  }
  .sm\:gap-x-5 {
    @media (width >= 40rem) {
      column-gap: calc(var(--spacing) * 5);
    }
  }
  .sm\:gap-y-0 {
    @media (width >= 40rem) {
      row-gap: calc(var(--spacing) * 0);
    }
  }
  .sm\:rounded-lg {
    @media (width >= 40rem) {
      border-radius: var(--radius-lg);
    }
  }
  .sm\:rounded-b-lg {
    @media (width >= 40rem) {
      border-bottom-right-radius: var(--radius-lg);
      border-bottom-left-radius: var(--radius-lg);
    }
  }
  .sm\:border-t-0 {
    @media (width >= 40rem) {
      border-top-style: var(--tw-border-style);
      border-top-width: 0px;
    }
  }
  .sm\:p-5 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 5);
    }
  }
  .sm\:p-8 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 8);
    }
  }
  .sm\:px-5 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 5);
    }
  }
  .sm\:px-6 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .sm\:py-0 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 0);
    }
  }
  .sm\:py-1\.5 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 1.5);
    }
  }
  .sm\:py-2 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 2);
    }
  }
  .sm\:py-2\.5 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 2.5);
    }
  }
  .sm\:py-3 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 3);
    }
  }
  .sm\:pb-2 {
    @media (width >= 40rem) {
      padding-bottom: calc(var(--spacing) * 2);
    }
  }
  .sm\:pb-16 {
    @media (width >= 40rem) {
      padding-bottom: calc(var(--spacing) * 16);
    }
  }
  .sm\:text-sm {
    @media (width >= 40rem) {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .sm\:text-xs {
    @media (width >= 40rem) {
      font-size: var(--text-xs);
      line-height: var(--tw-leading, var(--text-xs--line-height));
    }
  }
  .sm\:leading-3 {
    @media (width >= 40rem) {
      --tw-leading: calc(var(--spacing) * 3);
      line-height: calc(var(--spacing) * 3);
    }
  }
  .sm\:opacity-0 {
    @media (width >= 40rem) {
      opacity: 0%;
    }
  }
  .sm\:shadow-xl {
    @media (width >= 40rem) {
      --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .sm\:group-hover\:opacity-100 {
    @media (width >= 40rem) {
      &:is(:where(.group):hover *) {
        opacity: 100%;
      }
    }
  }
  .md\:me-3\! {
    @media (width >= 48rem) {
      margin-inline-end: calc(var(--spacing) * 3) !important;
    }
  }
  .md\:mt-1 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 1);
    }
  }
  .md\:h-96 {
    @media (width >= 48rem) {
      height: calc(var(--spacing) * 96);
    }
  }
  .md\:w-48 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 48);
    }
  }
  .md\:max-w-4xl {
    @media (width >= 48rem) {
      max-width: var(--container-4xl);
    }
  }
  .md\:rotate-0 {
    @media (width >= 48rem) {
      rotate: 0deg;
    }
  }
  .md\:grid-cols-2 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:flex-nowrap {
    @media (width >= 48rem) {
      flex-wrap: nowrap;
    }
  }
  .md\:justify-end {
    @media (width >= 48rem) {
      justify-content: flex-end;
    }
  }
  .md\:justify-start {
    @media (width >= 48rem) {
      justify-content: flex-start;
    }
  }
  .md\:gap-4 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 4);
    }
  }
  .md\:gap-x-3 {
    @media (width >= 48rem) {
      column-gap: calc(var(--spacing) * 3);
    }
  }
  .md\:gap-x-5 {
    @media (width >= 48rem) {
      column-gap: calc(var(--spacing) * 5);
    }
  }
  .md\:gap-y-0 {
    @media (width >= 48rem) {
      row-gap: calc(var(--spacing) * 0);
    }
  }
  .md\:rounded-lg {
    @media (width >= 48rem) {
      border-radius: var(--radius-lg);
    }
  }
  .md\:p-1 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 1);
    }
  }
  .md\:pt-5 {
    @media (width >= 48rem) {
      padding-top: calc(var(--spacing) * 5);
    }
  }
  .md\:pb-0 {
    @media (width >= 48rem) {
      padding-bottom: calc(var(--spacing) * 0);
    }
  }
  .md\:text-6xl {
    @media (width >= 48rem) {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }
  .md\:shadow-xl {
    @media (width >= 48rem) {
      --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .md\:duration-\[150ms\] {
    @media (width >= 48rem) {
      --tw-duration: 150ms;
      transition-duration: 150ms;
    }
  }
  .md\:\[--strategy\:absolute\] {
    @media (width >= 48rem) {
      --strategy: absolute;
    }
  }
  .md\:\[--trigger\:hover\] {
    @media (width >= 48rem) {
      --trigger: hover;
    }
  }
  .lg\:inset-y-auto {
    @media (width >= 64rem) {
      inset-block: auto;
    }
  }
  .lg\:end-auto {
    @media (width >= 64rem) {
      inset-inline-end: auto;
    }
  }
  .lg\:bottom-0 {
    @media (width >= 64rem) {
      bottom: calc(var(--spacing) * 0);
    }
  }
  .lg\:order-3 {
    @media (width >= 64rem) {
      order: 3;
    }
  }
  .lg\:mx-0 {
    @media (width >= 64rem) {
      margin-inline: calc(var(--spacing) * 0);
    }
  }
  .lg\:ms-5 {
    @media (width >= 64rem) {
      margin-inline-start: calc(var(--spacing) * 5);
    }
  }
  .lg\:ms-60 {
    @media (width >= 64rem) {
      margin-inline-start: calc(var(--spacing) * 60);
    }
  }
  .lg\:block {
    @media (width >= 64rem) {
      display: block;
    }
  }
  .lg\:grid {
    @media (width >= 64rem) {
      display: grid;
    }
  }
  .lg\:hidden {
    @media (width >= 64rem) {
      display: none;
    }
  }
  .lg\:size-9\.5 {
    @media (width >= 64rem) {
      width: calc(var(--spacing) * 9.5);
      height: calc(var(--spacing) * 9.5);
    }
  }
  .lg\:w-full {
    @media (width >= 64rem) {
      width: 100%;
    }
  }
  .lg\:translate-x-0 {
    @media (width >= 64rem) {
      --tw-translate-x: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .lg\:grid-cols-3 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .lg\:bg-transparent {
    @media (width >= 64rem) {
      background-color: transparent;
    }
  }
  .lg\:px-5 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 5);
    }
  }
  .lg\:px-8 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
  .lg\:ps-8 {
    @media (width >= 64rem) {
      padding-inline-start: calc(var(--spacing) * 8);
    }
  }
  .lg\:ps-60 {
    @media (width >= 64rem) {
      padding-inline-start: calc(var(--spacing) * 60);
    }
  }
  .lg\:ps-65 {
    @media (width >= 64rem) {
      padding-inline-start: calc(var(--spacing) * 65);
    }
  }
  .lg\:text-sm {
    @media (width >= 64rem) {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .xl\:col-span-1 {
    @media (width >= 80rem) {
      grid-column: span 1 / span 1;
    }
  }
  .xl\:col-span-2 {
    @media (width >= 80rem) {
      grid-column: span 2 / span 2;
    }
  }
  .xl\:grid {
    @media (width >= 80rem) {
      display: grid;
    }
  }
  .xl\:w-full {
    @media (width >= 80rem) {
      width: 100%;
    }
  }
  .xl\:grid-cols-3 {
    @media (width >= 80rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .dark\:divide-neutral-700 {
    &:where(.dark, .dark *) {
      :where(& > :not(:last-child)) {
        border-color: var(--color-neutral-700);
      }
    }
  }
  .dark\:divide-neutral-800 {
    &:where(.dark, .dark *) {
      :where(& > :not(:last-child)) {
        border-color: var(--color-neutral-800);
      }
    }
  }
  .dark\:border {
    &:where(.dark, .dark *) {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }
  .dark\:\!border-neutral-700 {
    &:where(.dark, .dark *) {
      border-color: var(--color-neutral-700) !important;
    }
  }
  .dark\:border-neutral-500 {
    &:where(.dark, .dark *) {
      border-color: var(--color-neutral-500);
    }
  }
  .dark\:border-neutral-600 {
    &:where(.dark, .dark *) {
      border-color: var(--color-neutral-600);
    }
  }
  .dark\:border-neutral-700 {
    &:where(.dark, .dark *) {
      border-color: var(--color-neutral-700);
    }
  }
  .dark\:border-neutral-800 {
    &:where(.dark, .dark *) {
      border-color: var(--color-neutral-800);
    }
  }
  .dark\:border-transparent {
    &:where(.dark, .dark *) {
      border-color: transparent;
    }
  }
  .dark\:\!bg-neutral-800 {
    &:where(.dark, .dark *) {
      background-color: var(--color-neutral-800) !important;
    }
  }
  .dark\:bg-blue-500 {
    &:where(.dark, .dark *) {
      background-color: var(--color-blue-500);
    }
  }
  .dark\:bg-blue-800\/30 {
    &:where(.dark, .dark *) {
      background-color: color-mix(in oklab, var(--color-blue-800) 30%, transparent);
    }
  }
  .dark\:bg-gray-800 {
    &:where(.dark, .dark *) {
      background-color: var(--color-gray-800);
    }
  }
  .dark\:bg-gray-800\/30 {
    &:where(.dark, .dark *) {
      background-color: color-mix(in oklab, var(--color-gray-800) 30%, transparent);
    }
  }
  .dark\:bg-green-800\/30 {
    &:where(.dark, .dark *) {
      background-color: color-mix(in oklab, var(--color-green-800) 30%, transparent);
    }
  }
  .dark\:bg-neutral-200 {
    &:where(.dark, .dark *) {
      background-color: var(--color-neutral-200);
    }
  }
  .dark\:bg-neutral-400 {
    &:where(.dark, .dark *) {
      background-color: var(--color-neutral-400);
    }
  }
  .dark\:bg-neutral-500 {
    &:where(.dark, .dark *) {
      background-color: var(--color-neutral-500);
    }
  }
  .dark\:bg-neutral-700 {
    &:where(.dark, .dark *) {
      background-color: var(--color-neutral-700);
    }
  }
  .dark\:bg-neutral-800 {
    &:where(.dark, .dark *) {
      background-color: var(--color-neutral-800);
    }
  }
  .dark\:bg-neutral-900 {
    &:where(.dark, .dark *) {
      background-color: var(--color-neutral-900);
    }
  }
  .dark\:bg-neutral-900\/80 {
    &:where(.dark, .dark *) {
      background-color: color-mix(in oklab, var(--color-neutral-900) 80%, transparent);
    }
  }
  .dark\:bg-neutral-950 {
    &:where(.dark, .dark *) {
      background-color: var(--color-neutral-950);
    }
  }
  .dark\:bg-purple-800\/30 {
    &:where(.dark, .dark *) {
      background-color: color-mix(in oklab, var(--color-purple-800) 30%, transparent);
    }
  }
  .dark\:bg-red-600 {
    &:where(.dark, .dark *) {
      background-color: var(--color-red-600);
    }
  }
  .dark\:bg-red-800\/30 {
    &:where(.dark, .dark *) {
      background-color: color-mix(in oklab, var(--color-red-800) 30%, transparent);
    }
  }
  .dark\:bg-transparent {
    &:where(.dark, .dark *) {
      background-color: transparent;
    }
  }
  .dark\:bg-violet-800 {
    &:where(.dark, .dark *) {
      background-color: var(--color-violet-800);
    }
  }
  .dark\:bg-yellow-800\/30 {
    &:where(.dark, .dark *) {
      background-color: color-mix(in oklab, var(--color-yellow-800) 30%, transparent);
    }
  }
  .dark\:bg-\[linear-gradient\(45deg\,var\(--color-neutral-700\)_7\.14\%\,transparent_7\.14\%\,transparent_50\%\,var\(--color-neutral-700\)_50\%\,var\(--color-neutral-700\)_57\.14\%\,transparent_57\.14\%\,transparent\)\;\] {
    &:where(.dark, .dark *) {
      background-image: linear-gradient(45deg,var(--color-neutral-700) 7.14%,transparent 7.14%,transparent 50%,var(--color-neutral-700) 50%,var(--color-neutral-700) 57.14%,transparent 57.14%,transparent);;
    }
  }
  .dark\:from-blue-500 {
    &:where(.dark, .dark *) {
      --tw-gradient-from: var(--color-blue-500);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }
  .dark\:from-neutral-800 {
    &:where(.dark, .dark *) {
      --tw-gradient-from: var(--color-neutral-800);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }
  .dark\:to-purple-500 {
    &:where(.dark, .dark *) {
      --tw-gradient-to: var(--color-purple-500);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }
  .dark\:fill-blue-500 {
    &:where(.dark, .dark *) {
      fill: var(--color-blue-500);
    }
  }
  .dark\:fill-neutral-200 {
    &:where(.dark, .dark *) {
      fill: var(--color-neutral-200);
    }
  }
  .dark\:fill-neutral-700 {
    &:where(.dark, .dark *) {
      fill: var(--color-neutral-700);
    }
  }
  .dark\:fill-neutral-700\/30 {
    &:where(.dark, .dark *) {
      fill: color-mix(in oklab, var(--color-neutral-700) 30%, transparent);
    }
  }
  .dark\:fill-neutral-700\/70 {
    &:where(.dark, .dark *) {
      fill: color-mix(in oklab, var(--color-neutral-700) 70%, transparent);
    }
  }
  .dark\:fill-neutral-800 {
    &:where(.dark, .dark *) {
      fill: var(--color-neutral-800);
    }
  }
  .dark\:fill-white {
    &:where(.dark, .dark *) {
      fill: var(--color-white);
    }
  }
  .dark\:stroke-neutral-500 {
    &:where(.dark, .dark *) {
      stroke: var(--color-neutral-500);
    }
  }
  .dark\:stroke-neutral-700\/10 {
    &:where(.dark, .dark *) {
      stroke: color-mix(in oklab, var(--color-neutral-700) 10%, transparent);
    }
  }
  .dark\:stroke-neutral-700\/30 {
    &:where(.dark, .dark *) {
      stroke: color-mix(in oklab, var(--color-neutral-700) 30%, transparent);
    }
  }
  .dark\:stroke-neutral-700\/60 {
    &:where(.dark, .dark *) {
      stroke: color-mix(in oklab, var(--color-neutral-700) 60%, transparent);
    }
  }
  .dark\:stroke-white {
    &:where(.dark, .dark *) {
      stroke: var(--color-white);
    }
  }
  .dark\:text-blue-400 {
    &:where(.dark, .dark *) {
      color: var(--color-blue-400);
    }
  }
  .dark\:text-blue-500 {
    &:where(.dark, .dark *) {
      color: var(--color-blue-500);
    }
  }
  .dark\:text-gray-500 {
    &:where(.dark, .dark *) {
      color: var(--color-gray-500);
    }
  }
  .dark\:text-green-500 {
    &:where(.dark, .dark *) {
      color: var(--color-green-500);
    }
  }
  .dark\:text-neutral-200 {
    &:where(.dark, .dark *) {
      color: var(--color-neutral-200);
    }
  }
  .dark\:text-neutral-300 {
    &:where(.dark, .dark *) {
      color: var(--color-neutral-300);
    }
  }
  .dark\:text-neutral-400 {
    &:where(.dark, .dark *) {
      color: var(--color-neutral-400);
    }
  }
  .dark\:text-neutral-500 {
    &:where(.dark, .dark *) {
      color: var(--color-neutral-500);
    }
  }
  .dark\:text-neutral-600 {
    &:where(.dark, .dark *) {
      color: var(--color-neutral-600);
    }
  }
  .dark\:text-neutral-700 {
    &:where(.dark, .dark *) {
      color: var(--color-neutral-700);
    }
  }
  .dark\:text-purple-500 {
    &:where(.dark, .dark *) {
      color: var(--color-purple-500);
    }
  }
  .dark\:text-red-500 {
    &:where(.dark, .dark *) {
      color: var(--color-red-500);
    }
  }
  .dark\:text-white {
    &:where(.dark, .dark *) {
      color: var(--color-white);
    }
  }
  .dark\:text-white\/60 {
    &:where(.dark, .dark *) {
      color: color-mix(in oklab, var(--color-white) 60%, transparent);
    }
  }
  .dark\:text-yellow-500 {
    &:where(.dark, .dark *) {
      color: var(--color-yellow-500);
    }
  }
  .dark\:placeholder-neutral-500 {
    &:where(.dark, .dark *) {
      &::placeholder {
        color: var(--color-neutral-500);
      }
    }
  }
  .dark\:ring-neutral-800 {
    &:where(.dark, .dark *) {
      --tw-ring-color: var(--color-neutral-800);
    }
  }
  .dark\:group-hover\:text-blue-600 {
    &:where(.dark, .dark *) {
      &:is(:where(.group):hover *) {
        color: var(--color-blue-600);
      }
    }
  }
  .dark\:group-focus\:text-blue-500 {
    &:where(.dark, .dark *) {
      &:is(:where(.group):focus *) {
        color: var(--color-blue-500);
      }
    }
  }
  .dark\:peer-checked\:bg-blue-500 {
    &:where(.dark, .dark *) {
      &:is(:where(.peer):checked ~ *) {
        background-color: var(--color-blue-500);
      }
    }
  }
  .dark\:peer-checked\:bg-white {
    &:where(.dark, .dark *) {
      &:is(:where(.peer):checked ~ *) {
        background-color: var(--color-white);
      }
    }
  }
  .dark\:placeholder\:text-neutral-400 {
    &:where(.dark, .dark *) {
      &::placeholder {
        color: var(--color-neutral-400);
      }
    }
  }
  .dark\:placeholder\:text-neutral-500 {
    &:where(.dark, .dark *) {
      &::placeholder {
        color: var(--color-neutral-500);
      }
    }
  }
  .dark\:placeholder\:text-white\/60 {
    &:where(.dark, .dark *) {
      &::placeholder {
        color: color-mix(in oklab, var(--color-white) 60%, transparent);
      }
    }
  }
  .dark\:before\:bg-neutral-600 {
    &:where(.dark, .dark *) {
      &::before {
        content: var(--tw-content);
        background-color: var(--color-neutral-600);
      }
    }
  }
  .dark\:before\:bg-neutral-700 {
    &:where(.dark, .dark *) {
      &::before {
        content: var(--tw-content);
        background-color: var(--color-neutral-700);
      }
    }
  }
  .dark\:before\:bg-\[url\(\'https\:\/\/preline\.co\/assets\/svg\/component-dark\/hero-gradient\.svg\'\)\] {
    &:where(.dark, .dark *) {
      &::before {
        content: var(--tw-content);
        background-image: url('https://preline.co/assets/svg/component-dark/hero-gradient.svg');
      }
    }
  }
  .dark\:after\:bg-neutral-700 {
    &:where(.dark, .dark *) {
      &::after {
        content: var(--tw-content);
        background-color: var(--color-neutral-700);
      }
    }
  }
  .dark\:first\:border-transparent {
    &:where(.dark, .dark *) {
      &:first-child {
        border-color: transparent;
      }
    }
  }
  .dark\:last\:border-b-transparent {
    &:where(.dark, .dark *) {
      &:last-child {
        border-bottom-color: transparent;
      }
    }
  }
  .dark\:checked\:border-blue-500 {
    &:where(.dark, .dark *) {
      &:checked {
        border-color: var(--color-blue-500);
      }
    }
  }
  .dark\:checked\:border-indigo-500 {
    &:where(.dark, .dark *) {
      &:checked {
        border-color: var(--color-indigo-500);
      }
    }
  }
  .dark\:checked\:bg-blue-500 {
    &:where(.dark, .dark *) {
      &:checked {
        background-color: var(--color-blue-500);
      }
    }
  }
  .dark\:checked\:bg-indigo-500 {
    &:where(.dark, .dark *) {
      &:checked {
        background-color: var(--color-indigo-500);
      }
    }
  }
  .dark\:hover\:border-blue-500 {
    &:where(.dark, .dark *) {
      &:hover {
        border-color: var(--color-blue-500);
      }
    }
  }
  .dark\:hover\:border-neutral-500 {
    &:where(.dark, .dark *) {
      &:hover {
        border-color: var(--color-neutral-500);
      }
    }
  }
  .dark\:hover\:border-neutral-700 {
    &:where(.dark, .dark *) {
      &:hover {
        border-color: var(--color-neutral-700);
      }
    }
  }
  .dark\:hover\:bg-gray-700 {
    &:where(.dark, .dark *) {
      &:hover {
        background-color: var(--color-gray-700);
      }
    }
  }
  .dark\:hover\:bg-neutral-600 {
    &:where(.dark, .dark *) {
      &:hover {
        background-color: var(--color-neutral-600);
      }
    }
  }
  .dark\:hover\:bg-neutral-700 {
    &:where(.dark, .dark *) {
      &:hover {
        background-color: var(--color-neutral-700);
      }
    }
  }
  .dark\:hover\:bg-neutral-800 {
    &:where(.dark, .dark *) {
      &:hover {
        background-color: var(--color-neutral-800);
      }
    }
  }
  .dark\:hover\:bg-red-800\/30 {
    &:where(.dark, .dark *) {
      &:hover {
        background-color: color-mix(in oklab, var(--color-red-800) 30%, transparent);
      }
    }
  }
  .dark\:hover\:bg-white\/10 {
    &:where(.dark, .dark *) {
      &:hover {
        background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
      }
    }
  }
  .dark\:hover\:text-blue-400 {
    &:where(.dark, .dark *) {
      &:hover {
        color: var(--color-blue-400);
      }
    }
  }
  .dark\:hover\:text-blue-500 {
    &:where(.dark, .dark *) {
      &:hover {
        color: var(--color-blue-500);
      }
    }
  }
  .dark\:hover\:text-blue-600 {
    &:where(.dark, .dark *) {
      &:hover {
        color: var(--color-blue-600);
      }
    }
  }
  .dark\:hover\:text-indigo-400 {
    &:where(.dark, .dark *) {
      &:hover {
        color: var(--color-indigo-400);
      }
    }
  }
  .dark\:hover\:text-neutral-200 {
    &:where(.dark, .dark *) {
      &:hover {
        color: var(--color-neutral-200);
      }
    }
  }
  .dark\:hover\:text-neutral-300 {
    &:where(.dark, .dark *) {
      &:hover {
        color: var(--color-neutral-300);
      }
    }
  }
  .dark\:hover\:text-white {
    &:where(.dark, .dark *) {
      &:hover {
        color: var(--color-white);
      }
    }
  }
  .dark\:hover\:text-white\/80 {
    &:where(.dark, .dark *) {
      &:hover {
        color: color-mix(in oklab, var(--color-white) 80%, transparent);
      }
    }
  }
  .dark\:focus\:border-blue-500 {
    &:where(.dark, .dark *) {
      &:focus {
        border-color: var(--color-blue-500);
      }
    }
  }
  .dark\:focus\:bg-neutral-600 {
    &:where(.dark, .dark *) {
      &:focus {
        background-color: var(--color-neutral-600);
      }
    }
  }
  .dark\:focus\:bg-neutral-700 {
    &:where(.dark, .dark *) {
      &:focus {
        background-color: var(--color-neutral-700);
      }
    }
  }
  .dark\:focus\:bg-neutral-800 {
    &:where(.dark, .dark *) {
      &:focus {
        background-color: var(--color-neutral-800);
      }
    }
  }
  .dark\:focus\:bg-neutral-900 {
    &:where(.dark, .dark *) {
      &:focus {
        background-color: var(--color-neutral-900);
      }
    }
  }
  .dark\:focus\:bg-red-800\/30 {
    &:where(.dark, .dark *) {
      &:focus {
        background-color: color-mix(in oklab, var(--color-red-800) 30%, transparent);
      }
    }
  }
  .dark\:focus\:text-blue-500 {
    &:where(.dark, .dark *) {
      &:focus {
        color: var(--color-blue-500);
      }
    }
  }
  .dark\:focus\:text-blue-600 {
    &:where(.dark, .dark *) {
      &:focus {
        color: var(--color-blue-600);
      }
    }
  }
  .dark\:focus\:text-indigo-400 {
    &:where(.dark, .dark *) {
      &:focus {
        color: var(--color-indigo-400);
      }
    }
  }
  .dark\:focus\:text-indigo-500 {
    &:where(.dark, .dark *) {
      &:focus {
        color: var(--color-indigo-500);
      }
    }
  }
  .dark\:focus\:text-neutral-300 {
    &:where(.dark, .dark *) {
      &:focus {
        color: var(--color-neutral-300);
      }
    }
  }
  .dark\:focus\:text-neutral-400 {
    &:where(.dark, .dark *) {
      &:focus {
        color: var(--color-neutral-400);
      }
    }
  }
  .dark\:focus\:ring-1 {
    &:where(.dark, .dark *) {
      &:focus {
        --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentColor);
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .dark\:focus\:ring-blue-500 {
    &:where(.dark, .dark *) {
      &:focus {
        --tw-ring-color: var(--color-blue-500);
      }
    }
  }
  .dark\:focus\:ring-neutral-600 {
    &:where(.dark, .dark *) {
      &:focus {
        --tw-ring-color: var(--color-neutral-600);
      }
    }
  }
  .dark\:focus\:ring-offset-gray-800 {
    &:where(.dark, .dark *) {
      &:focus {
        --tw-ring-offset-color: var(--color-gray-800);
      }
    }
  }
  .dark\:focus\:ring-offset-neutral-800 {
    &:where(.dark, .dark *) {
      &:focus {
        --tw-ring-offset-color: var(--color-neutral-800);
      }
    }
  }
  .dark\:focus\:outline-hidden {
    &:where(.dark, .dark *) {
      &:focus {
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
    }
  }
  .dark\:focus\:outline-none {
    &:where(.dark, .dark *) {
      &:focus {
        --tw-outline-style: none;
        outline-style: none;
      }
    }
  }
  .dark\:has-checked\:bg-neutral-600 {
    &:where(.dark, .dark *) {
      &:has(*:checked) {
        background-color: var(--color-neutral-600);
      }
    }
  }
  .hs-dropdown-open\:rotate-180 {
    &.hs-dropdown-menu.open {
      rotate: 180deg;
    }
    .hs-dropdown.open>& {
      rotate: 180deg;
    }
    .hs-dropdown.open>.hs-dropdown-toggle & {
      rotate: 180deg;
    }
    .hs-dropdown.open>.hs-dropdown-menu>& {
      rotate: 180deg;
    }
  }
  .hs-dropdown-open\:opacity-100 {
    &.hs-dropdown-menu.open {
      opacity: 100%;
    }
    .hs-dropdown.open>& {
      opacity: 100%;
    }
    .hs-dropdown.open>.hs-dropdown-toggle & {
      opacity: 100%;
    }
    .hs-dropdown.open>.hs-dropdown-menu>& {
      opacity: 100%;
    }
  }
  .hs-tooltip-shown\:visible {
    &.hs-tooltip-content.show {
      visibility: visible;
    }
    .hs-tooltip.show & {
      visibility: visible;
    }
  }
  .hs-tooltip-shown\:opacity-100 {
    &.hs-tooltip-content.show {
      opacity: 100%;
    }
    .hs-tooltip.show & {
      opacity: 100%;
    }
  }
  .hs-accordion-active\:-rotate-180 {
    &.hs-accordion.active {
      rotate: calc(180deg * -1);
    }
    .hs-accordion.active>& {
      rotate: calc(180deg * -1);
    }
    .hs-accordion.active>.hs-accordion-toggle & {
      rotate: calc(180deg * -1);
    }
    .hs-accordion.active>.hs-accordion-heading>.hs-accordion-toggle & {
      rotate: calc(180deg * -1);
    }
    &.hs-accordion-toggle {
      .hs-accordion.active>& {
        rotate: calc(180deg * -1);
      }
    }
    &.hs-accordion-toggle {
      .hs-accordion.active>.hs-accordion-heading>& {
        rotate: calc(180deg * -1);
      }
    }
    &.hs-accordion-force-active {
      .hs-accordion.active & {
        rotate: calc(180deg * -1);
      }
    }
  }
  .hs-accordion-active\:bg-gray-100 {
    &.hs-accordion.active {
      background-color: var(--color-gray-100);
    }
    .hs-accordion.active>& {
      background-color: var(--color-gray-100);
    }
    .hs-accordion.active>.hs-accordion-toggle & {
      background-color: var(--color-gray-100);
    }
    .hs-accordion.active>.hs-accordion-heading>.hs-accordion-toggle & {
      background-color: var(--color-gray-100);
    }
    &.hs-accordion-toggle {
      .hs-accordion.active>& {
        background-color: var(--color-gray-100);
      }
    }
    &.hs-accordion-toggle {
      .hs-accordion.active>.hs-accordion-heading>& {
        background-color: var(--color-gray-100);
      }
    }
    &.hs-accordion-force-active {
      .hs-accordion.active & {
        background-color: var(--color-gray-100);
      }
    }
  }
  .dark\:hs-accordion-active\:bg-neutral-700 {
    &:where(.dark, .dark *) {
      &.hs-accordion.active {
        background-color: var(--color-neutral-700);
      }
      .hs-accordion.active>& {
        background-color: var(--color-neutral-700);
      }
      .hs-accordion.active>.hs-accordion-toggle & {
        background-color: var(--color-neutral-700);
      }
      .hs-accordion.active>.hs-accordion-heading>.hs-accordion-toggle & {
        background-color: var(--color-neutral-700);
      }
      &.hs-accordion-toggle {
        .hs-accordion.active>& {
          background-color: var(--color-neutral-700);
        }
      }
      &.hs-accordion-toggle {
        .hs-accordion.active>.hs-accordion-heading>& {
          background-color: var(--color-neutral-700);
        }
      }
      &.hs-accordion-force-active {
        .hs-accordion.active & {
          background-color: var(--color-neutral-700);
        }
      }
    }
  }
  .hs-collapse-open\:hidden {
    &.hs-collapse.open {
      display: none;
    }
    &.hs-collapse-toggle.open {
      display: none;
    }
    .hs-collapse.open & {
      display: none;
    }
    .hs-collapse-toggle.open & {
      display: none;
    }
  }
  .hs-tab-active\:text-gray-800 {
    &[data-hs-tab].active {
      color: var(--color-gray-800);
    }
    [data-hs-tab].active & {
      color: var(--color-gray-800);
    }
  }
  .hs-tab-active\:after\:bg-gray-800 {
    &[data-hs-tab].active {
      &::after {
        content: var(--tw-content);
        background-color: var(--color-gray-800);
      }
    }
    [data-hs-tab].active & {
      &::after {
        content: var(--tw-content);
        background-color: var(--color-gray-800);
      }
    }
  }
  .dark\:hs-tab-active\:text-neutral-200 {
    &:where(.dark, .dark *) {
      &[data-hs-tab].active {
        color: var(--color-neutral-200);
      }
      [data-hs-tab].active & {
        color: var(--color-neutral-200);
      }
    }
  }
  .dark\:hs-tab-active\:after\:bg-neutral-400 {
    &:where(.dark, .dark *) {
      &[data-hs-tab].active {
        &::after {
          content: var(--tw-content);
          background-color: var(--color-neutral-400);
        }
      }
      [data-hs-tab].active & {
        &::after {
          content: var(--tw-content);
          background-color: var(--color-neutral-400);
        }
      }
    }
  }
  .hs-overlay-open\:mt-7 {
    &.open {
      margin-top: calc(var(--spacing) * 7);
    }
    .open & {
      margin-top: calc(var(--spacing) * 7);
    }
  }
  .hs-overlay-open\:translate-x-0 {
    &.open {
      --tw-translate-x: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
    .open & {
      --tw-translate-x: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .hs-overlay-open\:opacity-100 {
    &.open {
      opacity: 100%;
    }
    .open & {
      opacity: 100%;
    }
  }
  .hs-overlay-open\:duration-500 {
    &.open {
      --tw-duration: 500ms;
      transition-duration: 500ms;
    }
    .open & {
      --tw-duration: 500ms;
      transition-duration: 500ms;
    }
  }
  .hs-overlay-backdrop-open\:backdrop-blur-md {
    &.hs-overlay-backdrop {
      --tw-backdrop-blur: blur(var(--blur-md));
      -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
      backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    }
    .hs-overlay-backdrop & {
      --tw-backdrop-blur: blur(var(--blur-md));
      -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
      backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    }
  }
  .hs-selected\:block {
    &.selected {
      display: block;
    }
    .selected & {
      display: block;
    }
  }
  .hs-selected\:bg-gray-100 {
    &.selected {
      background-color: var(--color-gray-100);
    }
    .selected & {
      background-color: var(--color-gray-100);
    }
  }
  .dark\:hs-selected\:bg-gray-700 {
    &:where(.dark, .dark *) {
      &.selected {
        background-color: var(--color-gray-700);
      }
      .selected & {
        background-color: var(--color-gray-700);
      }
    }
  }
  .dark\:hs-selected\:bg-neutral-800 {
    &:where(.dark, .dark *) {
      &.selected {
        background-color: var(--color-neutral-800);
      }
      .selected & {
        background-color: var(--color-neutral-800);
      }
    }
  }
  .hs-select-disabled\:pointer-events-none {
    &.disabled {
      pointer-events: none;
    }
    .disabled & {
      pointer-events: none;
    }
  }
  .hs-select-disabled\:opacity-50 {
    &.disabled {
      opacity: 50%;
    }
    .disabled & {
      opacity: 50%;
    }
  }
  .hs-password-active\:block {
    &.active {
      display: block;
    }
    .active & {
      display: block;
    }
  }
  .hs-password-active\:hidden {
    &.active {
      display: none;
    }
    .active & {
      display: none;
    }
  }
  .hs-combo-box-selected\:block {
    &.selected {
      display: block;
    }
    .selected & {
      display: block;
    }
  }
  .hs-datatable-ordering-asc\:text-blue-600 {
    &.dt-ordering-asc {
      color: var(--color-blue-600);
    }
    .dt-ordering-asc & {
      color: var(--color-blue-600);
    }
  }
  .dark\:hs-datatable-ordering-asc\:text-blue-500 {
    &:where(.dark, .dark *) {
      &.dt-ordering-asc {
        color: var(--color-blue-500);
      }
      .dt-ordering-asc & {
        color: var(--color-blue-500);
      }
    }
  }
  .hs-datatable-ordering-desc\:text-blue-600 {
    &.dt-ordering-desc {
      color: var(--color-blue-600);
    }
    .dt-ordering-desc & {
      color: var(--color-blue-600);
    }
  }
  .dark\:hs-datatable-ordering-desc\:text-blue-500 {
    &:where(.dark, .dark *) {
      &.dt-ordering-desc {
        color: var(--color-blue-500);
      }
      .dt-ordering-desc & {
        color: var(--color-blue-500);
      }
    }
  }
  .hs-file-upload-complete\:bg-green-600 {
    &.complete {
      background-color: var(--color-green-600);
    }
    .complete & {
      background-color: var(--color-green-600);
    }
  }
  .hs-toastify-on\:opacity-100 {
    &.toastify.on {
      opacity: 100%;
    }
    .toastify.on & {
      opacity: 100%;
    }
  }
  .\[\&\:\:-webkit-scrollbar\]\:h-2 {
    &::-webkit-scrollbar {
      height: calc(var(--spacing) * 2);
    }
  }
  .\[\&\:\:-webkit-scrollbar\]\:w-2 {
    &::-webkit-scrollbar {
      width: calc(var(--spacing) * 2);
    }
  }
  .\[\&\:\:-webkit-scrollbar-thumb\]\:rounded-full {
    &::-webkit-scrollbar-thumb {
      border-radius: calc(infinity * 1px);
    }
  }
  .\[\&\:\:-webkit-scrollbar-thumb\]\:bg-gray-300 {
    &::-webkit-scrollbar-thumb {
      background-color: var(--color-gray-300);
    }
  }
  .dark\:\[\&\:\:-webkit-scrollbar-thumb\]\:bg-neutral-500 {
    &:where(.dark, .dark *) {
      &::-webkit-scrollbar-thumb {
        background-color: var(--color-neutral-500);
      }
    }
  }
  .\[\&\:\:-webkit-scrollbar-track\]\:bg-gray-100 {
    &::-webkit-scrollbar-track {
      background-color: var(--color-gray-100);
    }
  }
  .dark\:\[\&\:\:-webkit-scrollbar-track\]\:bg-neutral-700 {
    &:where(.dark, .dark *) {
      &::-webkit-scrollbar-track {
        background-color: var(--color-neutral-700);
      }
    }
  }
  .\[\&\>\.active\]\:bg-gray-100 {
    &>.active {
      background-color: var(--color-gray-100);
    }
  }
  .dark\:\[\&\>\.active\]\:bg-neutral-700 {
    &:where(.dark, .dark *) {
      &>.active {
        background-color: var(--color-neutral-700);
      }
    }
  }
  .\[\&\>\.toast-close\]\:block {
    &>.toast-close {
      display: block;
    }
  }
  .\[\&\>\.toast-close\]\:hidden {
    &>.toast-close {
      display: none;
    }
  }
}
@layer theme, base, components, utilities;
@layer theme;
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var( --default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji" );
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var( --default-font-variation-settings, normal );
    -webkit-tap-highlight-color: transparent;
  }
  body {
    line-height: inherit;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var( --default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace );
    font-feature-settings: var( --default-mono-font-feature-settings, normal );
    font-variation-settings: var( --default-mono-font-variation-settings, normal );
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
    color: color-mix(in oklab, currentColor 50%, transparent);
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities;
.vc {
  border-radius: var(--radius-xl);
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: var(--color-gray-200);
  background-color: var(--color-white);
  --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  &[data-vc-input] {
    position: absolute;
  }
  &[data-vc-type="default"] {
    padding: calc(var(--spacing) * 3);
  }
  &[data-vc-type="multiple"] {
    width: calc(var(--spacing) * 80);
    padding-block: calc(var(--spacing) * 3);
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 160);
    }
  }
  &[data-vc-calendar-hidden] {
    display: none;
  }
}
.dark .vc:not([data-vc-theme="light"]) {
  border-color: var(--color-neutral-700);
  background-color: var(--color-neutral-900);
}
.vc-week {
  display: flex;
  padding-bottom: calc(var(--spacing) * 1.5);
}
.vc-week__day {
  margin: 1px;
  display: block;
  width: calc(var(--spacing) * 10);
  text-align: center;
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
  --tw-font-weight: var(--font-weight-normal);
  font-weight: var(--font-weight-normal);
  color: var(--color-gray-500);
  &:focus {
    outline-style: none;
    @media (forced-colors: active) {
      outline: 2px solid transparent;
      outline-offset: 2px;
    }
  }
}
.dark .vc:not([data-vc-theme="light"]) .vc-week__day {
  color: var(--color-neutral-500);
}
.vc-dates {
  display: grid;
  grid-template-columns: repeat(7, minmax(0, 1fr));
  row-gap: calc(var(--spacing) * 0.5);
}
.vc-date {
  position: relative;
  display: flex;
  width: calc(var(--spacing) * 10.5);
  height: calc(var(--spacing) * 10.5);
  align-items: center;
  justify-content: center;
  border-radius: calc(infinity * 1px);
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
  color: var(--color-gray-800);
  &:empty {
    visibility: hidden;
  }
  &:hover {
    color: var(--color-blue-600);
    &::before {
      border-color: var(--color-blue-600);
    }
  }
  &:nth-child(7n) {
    border-top-right-radius: calc(infinity * 1px);
    border-bottom-right-radius: calc(infinity * 1px);
  }
  &:nth-child(7n+1) {
    border-top-left-radius: calc(infinity * 1px);
    border-bottom-left-radius: calc(infinity * 1px);
  }
  &::before {
    content: "";
    position: absolute;
    inset: calc(var(--spacing) * 0);
    width: 100%;
    height: 100%;
    border-radius: calc(infinity * 1px);
    border-style: var(--tw-border-style);
    border-width: 1.5px;
    border-color: transparent;
  }
  button {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: calc(infinity * 1px);
  }
  &[data-vc-date-month="prev"], &[data-vc-date-month="next"] {
    color: var(--color-gray-400);
  }
  &[data-vc-date-today] {
    background-color: var(--color-blue-600);
    color: var(--color-white);
  }
  &[data-vc-date-selected] {
    background-color: var(--color-blue-600);
    &:not([data-vc-date-selected="middle"]) {
      color: var(--color-white);
    }
    &[data-vc-date-hover="first-and-last"] {
      border-radius: calc(infinity * 1px);
    }
  }
  &[data-vc-date-hover="first"], &[data-vc-date-selected="first"] {
    border-top-left-radius: calc(infinity * 1px);
    border-bottom-left-radius: calc(infinity * 1px);
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    &::before {
      border-top-left-radius: calc(infinity * 1px);
      border-bottom-left-radius: calc(infinity * 1px);
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
  }
  &[data-vc-date-hover="last"], &[data-vc-date-selected="last"] {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: calc(infinity * 1px);
    border-bottom-right-radius: calc(infinity * 1px);
    &::before {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      border-top-right-radius: calc(infinity * 1px);
      border-bottom-right-radius: calc(infinity * 1px);
    }
  }
  &[data-vc-date-hover="first"][data-vc-date-selected], &[data-vc-date-hover="last"][data-vc-date-selected], &[data-vc-date-selected="first"], &[data-vc-date-selected="last"] {
    color: var(--color-white);
    &::before {
      background-color: var(--color-blue-600);
    }
  }
  &[data-vc-date-hover], &[data-vc-date-selected="middle"] {
    &:not([data-vc-date-hover="first"]):not([data-vc-date-hover="last"]):not([data-vc-date-hover="first-and-last"]) {
      border-radius: 0;
    }
    &:not([data-vc-date-today]):not([data-vc-date-hover="first-and-last"]) {
      background-color: var(--color-gray-100);
    }
  }
}
.dark .vc:not([data-vc-theme="light"]) .vc-date {
  color: var(--color-neutral-200);
  &[data-vc-date-month="prev"], &[data-vc-date-month="next"] {
    color: var(--color-neutral-600);
  }
  &[data-vc-date-hover], &[data-vc-date-selected="middle"] {
    &:not([data-vc-date-today]):not([data-vc-date-hover="first-and-last"]) {
      background-color: var(--color-neutral-800);
    }
  }
}
.vc-months {
  display: grid;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  gap: calc(var(--spacing) * 3);
}
.vc-months__month {
  display: inline-flex;
  align-items: center;
  column-gap: calc(var(--spacing) * 2);
  border-radius: var(--radius-lg);
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: var(--color-gray-200);
  padding-inline: calc(var(--spacing) * 3);
  padding-block: calc(var(--spacing) * 2);
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-800);
  &:hover {
    border-color: var(--color-gray-300);
    color: var(--color-gray-500);
  }
  &:focus {
    border-color: var(--color-gray-300);
    color: var(--color-gray-500);
    --tw-outline-style: none;
    outline-style: none;
  }
  &:disabled, &.disabled {
    pointer-events: none;
    opacity: 50%;
  }
  &[data-vc-months-month-selected] {
    border-color: var(--color-blue-600);
    background-color: var(--color-blue-600);
    color: var(--color-white);
  }
}
.dark .vc:not([data-vc-theme="light"]) .vc-months__month {
  border-color: var(--color-neutral-700);
  color: var(--color-neutral-200);
  &:hover, &:focus {
    border-color: var(--color-neutral-600);
    color: var(--color-neutral-500);
  }
  &[data-vc-months-month-selected] {
    border-color: var(--color-blue-500);
    background-color: var(--color-blue-500);
  }
}
.vc-month {
  color: var(--color-gray-800);
  &:hover, &:focus {
    color: var(--color-gray-600);
  }
}
.dark .vc:not([data-vc-theme="light"]) .vc-month {
  color: var(--color-neutral-200);
  &:hover, &:focus {
    color: var(--color-neutral-300);
  }
}
.vc-years {
  display: grid;
  grid-template-columns: repeat(5, minmax(0, 1fr));
  gap: calc(var(--spacing) * 2);
}
.vc-years__year {
  display: inline-flex;
  align-items: center;
  column-gap: calc(var(--spacing) * 2);
  border-radius: var(--radius-lg);
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: var(--color-gray-200);
  padding-inline: calc(var(--spacing) * 3);
  padding-block: calc(var(--spacing) * 2);
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-800);
  &:hover, &:focus {
    border-color: var(--color-gray-300);
    color: var(--color-gray-500);
  }
  &:focus {
    --tw-outline-style: none;
    outline-style: none;
  }
  &:disabled, &.disabled {
    pointer-events: none;
    opacity: 50%;
  }
  &[data-vc-years-year-selected] {
    border-color: var(--color-blue-600);
    background-color: var(--color-blue-600);
    color: var(--color-white);
  }
}
.dark .vc:not([data-vc-theme="light"]) .vc-years__year {
  border-color: var(--color-neutral-700);
  color: var(--color-neutral-200);
  &:hover, &:focus {
    border-color: var(--color-neutral-600);
    color: var(--color-neutral-500);
  }
  &[data-vc-years-year-selected] {
    border-color: var(--color-blue-500);
    background-color: var(--color-blue-500);
  }
}
.vc-year {
  color: var(--color-gray-800);
  &:hover, &:focus {
    color: var(--color-gray-600);
  }
}
.dark .vc:not([data-vc-theme="light"]) .vc-year {
  color: var(--color-neutral-200);
  &:hover, &:focus {
    color: var(--color-neutral-300);
  }
}
.vc-arrow {
  display: flex;
  width: calc(var(--spacing) * 8);
  height: calc(var(--spacing) * 8);
  align-items: center;
  justify-content: center;
  border-radius: calc(infinity * 1px);
  color: var(--color-gray-800);
  &:hover, &:focus {
    background-color: var(--color-gray-100);
  }
  &:focus {
    outline-style: none;
    @media (forced-colors: active) {
      outline: 2px solid transparent;
      outline-offset: 2px;
    }
  }
  &:disabled, &.disabled {
    pointer-events: none;
    opacity: 50%;
  }
}
.dark .vc:not([data-vc-theme="light"]) .vc-arrow {
  color: var(--color-neutral-400);
  &:hover, &:focus {
    background-color: var(--color-neutral-800);
  }
}
@layer base {
  button:not(:disabled), [role="button"]:not(:disabled) {
    cursor: pointer;
  }
}
.dt-layout-row:has(.dt-search), .dt-layout-row:has(.dt-length), .dt-layout-row:has(.dt-paging) {
  display: none !important;
}
@layer base {
  [type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {
    appearance: none;
    background-color: #fff;
    border-color: oklch(0.551 0.027 264.364);
    border-width: 1px;
    border-radius: 0px;
    padding-top: 0.5rem;
    padding-right: 0.75rem;
    padding-bottom: 0.5rem;
    padding-left: 0.75rem;
    font-size: 1rem;
    line-height: 1.5rem;
    --tw-shadow: 0 0 #0000;
    &:focus {
      outline: 2px solid transparent;
      outline-offset: 2px;
      --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-color: oklch(0.546 0.245 262.881);
      --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
      --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
      box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      border-color: oklch(0.546 0.245 262.881);
    }
  }
  input::placeholder,textarea::placeholder {
    color: oklch(0.551 0.027 264.364);
    opacity: 1;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-date-and-time-value {
    min-height: 1.5em;
  }
  ::-webkit-date-and-time-value {
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {
    padding-top: 0;
    padding-bottom: 0;
  }
  select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='oklch(0.551 0.027 264.364)' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    print-color-adjust: exact;
  }
  [multiple],[size]:where(select:not([size="1"])) {
    background-image: initial;
    background-position: initial;
    background-repeat: unset;
    background-size: initial;
    padding-right: 0.75rem;
    print-color-adjust: unset;
  }
  [type='checkbox'],[type='radio'] {
    appearance: none;
    padding: 0;
    print-color-adjust: exact;
    display: inline-block;
    vertical-align: middle;
    background-origin: border-box;
    user-select: none;
    flex-shrink: 0;
    height: 1rem;
    width: 1rem;
    color: oklch(0.546 0.245 262.881);
    background-color: #fff;
    border-color: oklch(0.551 0.027 264.364);
    border-width: 1px;
    --tw-shadow: 0 0 #0000;
  }
  [type='checkbox'] {
    border-radius: 0px;
  }
  [type='radio'] {
    border-radius: 100%;
  }
  [type='checkbox']:focus,[type='radio']:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: oklch(0.546 0.245 262.881);
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  [type='checkbox']:checked,[type='radio']:checked {
    border-color: transparent;
    background-color: currentColor;
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
  }
  [type='checkbox']:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
    @media (forced-colors: active) {
      appearance: auto;
    }
  }
  [type='radio']:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
    @media (forced-colors: active) {
      appearance: auto;
    }
  }
  [type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {
    border-color: transparent;
    background-color: currentColor;
  }
  [type='checkbox']:indeterminate {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
    border-color: transparent;
    background-color: currentColor;
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
    @media (forced-colors: active) {
      appearance: auto;
    }
  }
  [type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {
    border-color: transparent;
    background-color: currentColor;
  }
  [type='file'] {
    background: unset;
    border-color: inherit;
    border-width: 0;
    border-radius: 0;
    padding: 0;
    font-size: unset;
    line-height: inherit;
  }
  [type='file']:focus {
    outline: 1px solid ButtonText;
    outline: 1px auto -webkit-focus-ring-color;
  }
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
  initial-value: rotateX(0);
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
  initial-value: rotateY(0);
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
  initial-value: rotateZ(0);
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
  initial-value: skewX(0);
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
  initial-value: skewY(0);
}
@property --tw-pan-x {
  syntax: "*";
  inherits: false;
}
@property --tw-pan-y {
  syntax: "*";
  inherits: false;
}
@property --tw-pinch-zoom {
  syntax: "*";
  inherits: false;
}
@property --tw-scroll-snap-strictness {
  syntax: "*";
  inherits: false;
  initial-value: proximity;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-divide-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-ordinal {
  syntax: "*";
  inherits: false;
}
@property --tw-slashed-zero {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-figure {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-spacing {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-fraction {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@property --tw-contain-size {
  syntax: "*";
  inherits: false;
}
@property --tw-contain-layout {
  syntax: "*";
  inherits: false;
}
@property --tw-contain-paint {
  syntax: "*";
  inherits: false;
}
@property --tw-contain-style {
  syntax: "*";
  inherits: false;
}
@property --tw-content {
  syntax: "*";
  initial-value: "";
  inherits: false;
}
